# WIDDX Modern Theme for FOSSBilling

A modern, responsive client area theme for FOSSBilling with contemporary design patterns, smooth animations, and extensive customization options.

## Features

### 🎨 Modern Design
- Clean, contemporary interface with modern color schemes
- Responsive design that works on all devices
- CSS Grid and Flexbox layouts for optimal performance
- Modern typography with Inter font family
- Smooth animations and transitions
- Pure CSS implementation (no framework dependencies)

### 🚀 Performance Optimized
- Webpack-based build system for optimized assets
- CSS and JavaScript minification
- Modern browser support with fallbacks
- Lazy loading and performance optimizations
- Optimized asset loading with fallback support

### 🎛️ Highly Customizable
- Comprehensive theme settings panel matching FOSSBilling standards
- Color scheme customization
- Layout options (fluid/boxed)
- Typography settings
- Feature toggles
- Custom CSS support
- Complete FOSSBilling settings compatibility

### 📱 Mobile-First
- Responsive navigation with mobile-optimized menu
- Touch-friendly interface elements
- Optimized for mobile performance
- Progressive Web App ready
- Mobile hamburger menu with accessibility

### ♿ Accessibility
- WCAG 2.1 compliant
- Keyboard navigation support
- Screen reader friendly
- High contrast support
- ARIA attributes and semantic HTML

### 🔧 FOSSBilling Integration
- Complete FOSSBilling API integration
- API forms and links support
- Flash message system
- Currency selector functionality
- Tooltip system
- Loading states and spinners
- Form validation and feedback
- Pagination support
- Data tables with sorting
- Status badges and alerts

### 🎭 Component Library
- Comprehensive macro functions
- Status badges with color coding
- Modern buttons with icons
- Card components
- Alert messages with icons
- Loading spinners
- Progress bars
- Avatar components
- Breadcrumb navigation
- Data tables
- Empty state displays

## Installation

1. Copy the `widdx` folder to your FOSSBilling `themes` directory
2. Install dependencies:
   ```bash
   cd themes/widdx
   npm install
   ```
3. Build the theme assets:
   ```bash
   npm run build
   ```
4. Activate the theme in your FOSSBilling admin panel

## Development

### Prerequisites
- Node.js 16+ and npm
- FOSSBilling installation

### Building Assets

For development with hot reloading:
```bash
npm run dev
```

For development with file watching:
```bash
npm run watch
```

For production build:
```bash
npm run build
```

### File Structure

```
widdx/
├── assets/
│   ├── js/
│   │   └── widdx.js          # Main JavaScript file
│   ├── scss/
│   │   ├── widdx.scss        # Main SCSS file
│   │   └── markdown.scss     # Markdown styling
│   └── img/                  # Theme images
├── build/                    # Compiled assets (auto-generated)
├── config/
│   └── settings.html.twig    # Theme configuration panel
├── html/
│   ├── layout_default.html.twig    # Main layout
│   ├── layout_public.html.twig     # Public pages layout
│   ├── error.html.twig             # Error page template
│   ├── macro_functions.html.twig   # Utility macros
│   ├── partial_menu.html.twig      # Navigation menu
│   ├── mobile_menu.html.twig       # Mobile menu
│   ├── partial_message.html.twig   # Flash messages
│   ├── partial_pagination.html.twig # Pagination
│   ├── partial_pending_messages.html.twig # Pending notifications
│   └── partial_pricing.html.twig   # Pricing components
├── manifest.json             # Theme manifest
├── package.json             # Node.js dependencies
├── webpack.config.js        # Webpack configuration
├── postcss.config.js        # PostCSS configuration
└── README.md               # This file
```

## Customization

### Theme Settings

Access the theme settings through:
Admin Panel → Extensions → Themes → WIDDX → Settings

Available customization options:
- **Color Scheme**: Primary and secondary colors
- **Layout**: Fluid or boxed layout, sidebar position
- **Typography**: Font family and size options
- **Features**: Toggle animations, dark mode, breadcrumbs, etc.
- **Custom CSS**: Add your own CSS overrides

### CSS Variables

The theme uses CSS custom properties for easy customization:

```css
:root {
  --widdx-primary: #2563eb;
  --widdx-secondary: #64748b;
  --widdx-success: #10b981;
  --widdx-warning: #f59e0b;
  --widdx-danger: #ef4444;
  --widdx-info: #06b6d4;
}
```

### Extending the Theme

#### Adding Custom Templates

Create new templates in the `html` directory following FOSSBilling's template structure. The theme will automatically override default module templates.

#### Custom JavaScript

Add custom JavaScript to `assets/js/widdx.js` or create new files and import them.

#### Custom Styles

Add custom SCSS to `assets/scss/widdx.scss` or create new SCSS files and import them.

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This theme is released under the MIT License. See LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Contact WIDDX support
- Check FOSSBilling documentation

## Changelog

### Version 1.0.0
- Initial release
- Modern responsive design
- Comprehensive customization options
- Performance optimizations
- Accessibility improvements

## Credits

- Built for [FOSSBilling](https://fossbilling.org/)
- Uses [Bootstrap 5](https://getbootstrap.com/)
- Icons by [Font Awesome](https://fontawesome.com/)
- Fonts by [Google Fonts](https://fonts.google.com/)
- Animations by [Animate.css](https://animate.style/)

---

Made with ❤️ by WIDDX

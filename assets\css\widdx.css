/* WIDDX Modern Theme - Pure CSS Version */
/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Inter', system-ui, -apple-system, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: #1e293b;
  background-color: #f8fafc;
  font-feature-settings: "kern" 1, "liga" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 0.5rem;
  color: #0f172a;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: 1rem;
}

a {
  color: #2563eb;
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

a:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.75rem;
}

.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
  padding: 0 0.75rem;
}

.col-9 {
  flex: 0 0 75%;
  max-width: 75%;
  padding: 0 0.75rem;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
  padding: 0 0.75rem;
}

/* Header and Navigation */
header {
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
  min-height: 70px;
  flex-wrap: wrap;
}

.navbar-expand-md .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
}

.navbar-toggler {
  padding: 0.25rem 0.5rem;
  font-size: 1.25rem;
  line-height: 1;
  background-color: transparent;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  transition: box-shadow 0.15s ease-in-out;
  cursor: pointer;
}

.navbar-toggler:focus {
  text-decoration: none;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25);
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: #0f172a;
  text-decoration: none;
  margin-right: 1rem;
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
}

.navbar-brand:hover {
  color: #2563eb;
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  align-items: center;
  flex-direction: row;
  gap: 1rem;
}

.nav-item {
  position: relative;
}

.nav-link {
  color: #64748b;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  display: block;
  text-decoration: none;
}

.nav-link:hover {
  color: #2563eb;
  background-color: #f1f5f9;
  text-decoration: none;
}

/* Language Selector */
.js-language-selector {
  min-width: 120px;
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  background-color: #ffffff;
  color: #0f172a;
}

/* Dropdown Styles */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  background: none;
  border: none;
  color: #64748b;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dropdown-toggle:hover {
  color: #2563eb;
  background-color: #f1f5f9;
}

.dropdown-toggle:after {
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
  margin-left: 0.5rem;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  display: none;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 0.875rem;
  color: #0f172a;
  text-align: left;
  list-style: none;
  background-color: #ffffff;
  background-clip: padding-box;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  clear: both;
  font-weight: 400;
  color: #0f172a;
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  transition: all 0.2s ease-in-out;
}

.dropdown-item:hover {
  color: #2563eb;
  background-color: #f1f5f9;
  text-decoration: none;
}

.dropdown-item:focus {
  color: #2563eb;
  background-color: #f1f5f9;
  text-decoration: none;
  outline: 0;
}

/* Button Styles */
.btn {
  display: inline-block;
  font-weight: 500;
  line-height: 1.5;
  color: #0f172a;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.btn:hover {
  text-decoration: none;
}

.btn-outline-primary {
  color: #2563eb;
  border-color: #2563eb;
}

.btn-outline-primary:hover {
  color: #ffffff;
  background-color: #2563eb;
  border-color: #2563eb;
}

/* Responsive Navbar */
@media (max-width: 767.98px) {
  .navbar-collapse {
    display: none !important;
  }

  .navbar-collapse.show {
    display: block !important;
  }

  .navbar-nav {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    margin-top: 1rem;
    gap: 0;
  }

  .nav-item {
    width: 100%;
    margin-left: 0;
  }

  .nav-link {
    padding: 0.75rem 1rem;
    width: 100%;
  }
}

/* Tooltip Styles */
.widdx-tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  font-family: inherit;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 1;
  background-color: #000;
  color: #fff;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  max-width: 200px;
}

/* Alert Styles */
.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
}

.alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.alert-warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeaa7;
}

.alert-error,
.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.alert-dismissible {
  padding-right: 4rem;
}

.btn-close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  padding: 0.75rem 1.25rem;
  color: inherit;
  background: transparent;
  border: 0;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  opacity: 0.5;
  cursor: pointer;
}

.btn-close:hover {
  opacity: 0.75;
}

.btn-close:before {
  content: "×";
}

/* Required field indicator */
.text-danger {
  color: #dc3545 !important;
}

/* Form validation styles */
.is-invalid {
  border-color: #dc3545;
}

.is-valid {
  border-color: #28a745;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

.valid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #28a745;
}

/* Loading states */
.loading {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1rem;
}

.loading:after {
  content: '';
  display: inline-block;
  width: 1rem;
  height: 1rem;
  margin-left: 0.5rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* API form styles */
.api-form {
  position: relative;
}

.api-form.loading {
  opacity: 0.6;
  pointer-events: none;
}

.api-link {
  cursor: pointer;
  text-decoration: none;
}

.api-link:hover {
  text-decoration: underline;
}

/* Table styles for data tables */
.table {
  width: 100%;
  margin-bottom: 1rem;
  color: var(--widdx-text-color);
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid var(--widdx-border-color);
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid var(--widdx-border-color);
  background-color: var(--widdx-bg-secondary);
  font-weight: 600;
}

.table tbody + tbody {
  border-top: 2px solid var(--widdx-border-color);
}

.table-hover tbody tr:hover {
  background-color: var(--widdx-bg-secondary);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Pagination styles */
.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.375rem;
  justify-content: center;
  margin: 1rem 0;
}

.page-item {
  margin: 0 0.125rem;
}

.page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: var(--widdx-primary);
  text-decoration: none;
  background-color: var(--widdx-bg-primary);
  border: 1px solid var(--widdx-border-color);
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.page-link:hover {
  z-index: 2;
  color: var(--widdx-primary-dark);
  text-decoration: none;
  background-color: var(--widdx-bg-secondary);
  border-color: var(--widdx-border-color);
}

.page-item.active .page-link {
  z-index: 3;
  color: white;
  background-color: var(--widdx-primary);
  border-color: var(--widdx-primary);
}

.page-item.disabled .page-link {
  color: #6c757d;
  pointer-events: none;
  cursor: auto;
  background-color: var(--widdx-bg-primary);
  border-color: var(--widdx-border-color);
  opacity: 0.6;
}

/* Badge styles */
.badge {
  display: inline-block;
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.375rem;
}

.badge.rounded-pill {
  border-radius: 10rem;
}

.bg-primary {
  background-color: var(--widdx-primary) !important;
}

.bg-secondary {
  background-color: #6c757d !important;
}

.bg-success {
  background-color: #198754 !important;
}

.bg-danger {
  background-color: #dc3545 !important;
}

.bg-warning {
  background-color: #ffc107 !important;
}

.bg-info {
  background-color: #0dcaf0 !important;
}

.bg-light {
  background-color: #f8f9fa !important;
  color: #000 !important;
}

.bg-dark {
  background-color: #212529 !important;
}

.text-dark {
  color: #212529 !important;
}

/* Avatar styles */
.avatar-sm {
  width: 2rem;
  height: 2rem;
  font-size: 0.875rem;
}

.avatar-md {
  width: 2.5rem;
  height: 2.5rem;
  font-size: 1rem;
}

.avatar-lg {
  width: 3rem;
  height: 3rem;
  font-size: 1.125rem;
}

.avatar-xl {
  width: 4rem;
  height: 4rem;
  font-size: 1.25rem;
}

.avatar-initial {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

/* Progress bar styles */
.progress {
  display: flex;
  height: 1rem;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: #e9ecef;
  border-radius: 0.375rem;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: var(--widdx-primary);
  transition: width 0.6s ease;
}

.progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
}

.progress-bar-animated {
  animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
  0% {
    background-position-x: 1rem;
  }
}

/* Breadcrumb styles */
.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  list-style: none;
  background-color: var(--widdx-bg-secondary);
  border-radius: 0.375rem;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: 0.5rem;
  color: #6c757d;
  content: "/";
}

.breadcrumb-item.active {
  color: #6c757d;
}

/* Spinner styles */
.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border 0.75s linear infinite;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.125em;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}

/* Utility classes */
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Sidebar Menu */
.sidebar-menu {
  background: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-bottom: 1rem;
}

.sidebar-menu .nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu .nav-item {
  margin-bottom: 0.25rem;
}

.sidebar-menu .nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #64748b;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.sidebar-menu .nav-link:hover {
  background-color: #f1f5f9;
  color: #2563eb;
  transform: translateX(4px);
}

.sidebar-menu .nav-link i {
  margin-right: 0.5rem;
  width: 1.25rem;
  text-align: center;
}

/* Cards */
.card {
  background: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease-in-out;
  margin-bottom: 1rem;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 0.75rem 0.75rem 0 0;
  font-weight: 600;
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  border-radius: 0 0 0.75rem 0.75rem;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  background: none;
}

.btn:hover {
  transform: translateY(-1px);
  text-decoration: none;
}

.btn-primary {
  color: #ffffff;
  background-color: #2563eb;
  border-color: #2563eb;
}

.btn-primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
  color: #ffffff;
}

.btn-secondary {
  color: #ffffff;
  background-color: #64748b;
  border-color: #64748b;
}

.btn-secondary:hover {
  background-color: #475569;
  border-color: #475569;
  color: #ffffff;
}

.btn-success {
  color: #ffffff;
  background-color: #10b981;
  border-color: #10b981;
}

.btn-success:hover {
  background-color: #059669;
  border-color: #059669;
  color: #ffffff;
}

.btn-danger {
  color: #ffffff;
  background-color: #ef4444;
  border-color: #ef4444;
}

.btn-danger:hover {
  background-color: #dc2626;
  border-color: #dc2626;
  color: #ffffff;
}

.btn-outline-primary {
  color: #2563eb;
  border-color: #2563eb;
}

.btn-outline-primary:hover {
  color: #ffffff;
  background-color: #2563eb;
  border-color: #2563eb;
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #1f2937;
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.form-control:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-select {
  display: block;
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #1f2937;
  background-color: #ffffff;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  appearance: none;
}

.form-select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Tables */
.table {
  width: 100%;
  margin-bottom: 1rem;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
  text-align: left;
}

.table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
}

.table tbody tr:hover {
  background-color: #f9fafb;
}

.table-modern {
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Alerts */
.alert {
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
  border-radius: 0.75rem;
  border: none;
  border-left: 4px solid;
}

.alert-success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
  color: #065f46;
  border-left-color: #10b981;
}

.alert-danger {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
  color: #991b1b;
  border-left-color: #ef4444;
}

.alert-warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
  color: #92400e;
  border-left-color: #f59e0b;
}

.alert-info {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(6, 182, 212, 0.05));
  color: #155e75;
  border-left-color: #06b6d4;
}

/* Pagination */
.pagination {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 1rem 0;
  justify-content: center;
}

.page-item {
  margin: 0 0.125rem;
}

.page-link {
  display: block;
  padding: 0.5rem 0.75rem;
  color: #64748b;
  text-decoration: none;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  font-weight: 500;
}

.page-link:hover {
  background-color: #2563eb;
  border-color: #2563eb;
  color: #ffffff;
  transform: translateY(-1px);
}

.page-item.active .page-link {
  background-color: #2563eb;
  border-color: #2563eb;
  color: #ffffff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Mobile Menu */
.mobile-menu {
  padding: 1rem;
}

.mobile-menu .user-profile-section {
  background-color: #f1f5f9;
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.mobile-menu .nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-menu .nav-item {
  margin-bottom: 0.25rem;
}

.mobile-menu .nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  color: #374151;
  text-decoration: none;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.mobile-menu .nav-link:hover {
  background-color: #f3f4f6;
  transform: translateX(4px);
}

.mobile-menu .nav-link i {
  margin-right: 0.5rem;
  width: 1.25rem;
  text-align: center;
}

/* Avatar */
.avatar-md {
  width: 48px;
  height: 48px;
}

.avatar-initial {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 50%;
}

/* Balance Widget */
.balance-widget {
  background-color: #f1f5f9;
  border-radius: 0.75rem;
  padding: 1rem;
}

.balance-widget h6 {
  color: #64748b;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.balance-widget h5 {
  color: #2563eb;
  font-size: 1.25rem;
  margin-bottom: 0;
}

/* Sidebar Note */
.sidebar-note {
  background-color: rgba(6, 182, 212, 0.1);
  border-radius: 0.75rem;
  padding: 1rem;
}

.sidebar-note h6 {
  color: #0891b2;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.sidebar-note small {
  color: #64748b;
  font-size: 0.75rem;
}

/* Utility Classes */
.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }
.d-inline-block { display: inline-block; }

.align-items-center { align-items: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-center { justify-content: center; }
.justify-content-end { justify-content: end; }

.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: #2563eb; }
.text-secondary { color: #64748b; }
.text-success { color: #10b981; }
.text-danger { color: #ef4444; }
.text-warning { color: #f59e0b; }
.text-info { color: #06b6d4; }
.text-muted { color: #9ca3af; }

.bg-primary { background-color: #2563eb; }
.bg-secondary { background-color: #64748b; }
.bg-success { background-color: #10b981; }
.bg-danger { background-color: #ef4444; }
.bg-warning { background-color: #f59e0b; }
.bg-info { background-color: #06b6d4; }
.bg-light { background-color: #f8fafc; }
.bg-dark { background-color: #0f172a; }

.rounded { border-radius: 0.5rem; }
.rounded-circle { border-radius: 50%; }

.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }

.me-1 { margin-right: 0.25rem; }
.me-2 { margin-right: 0.5rem; }
.me-3 { margin-right: 1rem; }

.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.pt-3 { padding-top: 1rem; }

.fw-normal { font-weight: 400; }
.fw-medium { font-weight: 500; }
.fw-semibold { font-weight: 600; }
.fw-bold { font-weight: 700; }

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }

  .col-3, .col-9 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .navbar {
    flex-direction: column;
    padding: 0.5rem 0;
  }

  .navbar-nav {
    margin-top: 1rem;
    flex-direction: column;
    width: 100%;
  }

  .nav-item {
    margin: 0.25rem 0;
    margin-left: 0;
  }

  .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .card {
    margin-bottom: 1rem;
  }

  .d-md-none {
    display: block !important;
  }

  .d-none.d-md-block {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .d-md-none {
    display: none !important;
  }

  .d-none.d-md-block {
    display: block !important;
  }
}

/* Error Page Styles */
.error-page {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-code h1 {
  background: linear-gradient(135deg, #2563eb, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 6rem;
  font-weight: 700;
}

/* Footer Styles */
.footer {
  background: linear-gradient(135deg, #0f172a, #1e293b);
  color: #e2e8f0;
  padding: 2rem 0;
  margin-top: 2rem;
}

.footer h5, .footer h6 {
  color: #ffffff;
}

.footer a {
  color: #cbd5e1;
  transition: all 0.2s ease-in-out;
}

.footer a:hover {
  color: #ffffff;
  transform: translateX(2px);
}

/* Breadcrumb */
.breadcrumb {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 1rem 0;
  background: none;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: "/";
  margin: 0 0.5rem;
  color: #9ca3af;
}

.breadcrumb-item a {
  color: #64748b;
  text-decoration: none;
}

.breadcrumb-item a:hover {
  color: #2563eb;
}

.breadcrumb-item.active {
  color: #374151;
}

/* List Styles */
.list-unstyled {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* Text Decoration */
.text-decoration-none {
  text-decoration: none;
}

.text-decoration-none:hover {
  text-decoration: none;
}

/* Grid System */
.d-grid {
  display: grid;
}

.gap-2 {
  gap: 0.5rem;
}

/* Dropdown Styles */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.dropdown-toggle:hover {
  background-color: #f1f5f9;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  z-index: 1000;
  display: none;
  list-style: none;
  padding: 0.5rem 0;
  margin: 0.25rem 0 0 0;
}

.dropdown:hover .dropdown-menu {
  display: block;
}

.dropdown-item {
  display: block;
  padding: 0.5rem 1rem;
  color: #374151;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
}

.dropdown-item:hover {
  background-color: #f3f4f6;
  color: #2563eb;
}

/* Content Block */
.content-block {
  background: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

/* Welcome Message */
.welcome-message {
  background: linear-gradient(135deg, #2563eb, #06b6d4);
  color: #ffffff;
  padding: 2rem;
  border-radius: 0.75rem;
  margin-bottom: 2rem;
}

.welcome-message h1 {
  color: #ffffff;
  margin-bottom: 1rem;
}

/* Profile Section */
.profile-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Focus States */
*:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

.btn:focus,
.form-control:focus,
.form-select:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Print Styles */
@media print {
  .navbar,
  .sidebar-menu,
  .mobile-menu,
  .footer {
    display: none;
  }

  .col-9 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .card {
    box-shadow: none;
    border: 1px solid #000;
  }
}

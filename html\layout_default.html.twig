<!DOCTYPE html>
<html lang="en" data-bs-theme="{{ settings.theme|default('light') }}">
<head>
    <meta charset="utf-8">
    <title>{{ settings.meta_title_prefix }} {% block meta_title %}{% endblock %} {{ settings.meta_title_suffix }}</title>

    <meta property="bb:url" content="{{ constant('SYSTEM_URL') }}">
    <meta property="bb:client_area" content="{{ '/'|link }}">
    <meta name="csrf-token" content="{{ CSRFToken }}">

    <meta name="description" content="{% block meta_description %}{{ settings.meta_description }}{% endblock %}">
    <meta name="keywords" content="{{ settings.meta_keywords }}">
    <meta name="robots" content="{{ settings.meta_robots }}">
    <meta name="author" content="{{ settings.meta_author }}">
    <meta name="generator" content="FOSSBilling">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    {% block opengraph %}{% endblock %}
    
    <!-- Modern CSS and Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <!-- WIDDX Theme Assets -->
    <link rel='stylesheet' type='text/css' href="{{ 'css/widdx.css' | asset_url }}">

    <link rel="shortcut icon" href="{{ guest.system_company.favicon_url }}">

    {{ "Api/API.js" | library_url | script_tag }}
    <script src="{{ 'js/widdx.js' | asset_url }}"></script>

    {{ DebugBar_renderHead() }}

    {% block head %}{% endblock %}
    {% block js %}{% endblock %}
</head>

<body class="{% block body_class %}{% endblock %}">

{% block body %}
{% if not client and settings.require_login %}
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const publicPaths = [
                '/news',
                '/tos',
                '/privacy-policy'
            ];
            const currentPath = window.location.pathname;

            const isAllowed = publicPaths.some(path => currentPath.startsWith(path));
            if (!isAllowed) {
                window.location.href = '{{ "login"|link }}';
            }
        });
    </script>
{% endif %}

{% if client %}
    {% set profile = client.profile_get %}
{% endif %}

<!-- Modern Navigation Header -->
<header>
    <div class="container">
        <nav class="navbar navbar-expand-md">
            <!-- Brand Logo -->
            {% set company = guest.system_company %}
            {% if settings.show_company_logo and company.logo_url %}
                <a class="navbar-brand" href="{{ '/'|link }}">
                    <img src="{{ company.logo_url }}" alt="{{ company.name }}"
                         style="height: 32px; width: auto;" title="{{ company.name }}">
                    <span class="d-sm-none">{{ company.name }}</span>
                </a>
            {% else %}
                <a class="navbar-brand" href="{{ '/'|link }}">
                    <span>{{ company.name }}</span>
                </a>
            {% endif %}

            <!-- Mobile Menu Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                    data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                    aria-expanded="false" aria-label="{{ 'Toggle navigation'|trans }}">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Collapsible Navigation -->
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <div class="navbar-nav me-auto mb-2 mb-lg-0 w-100 d-flex justify-content-end">

                    <!-- Language Selector -->
                    {% set languages = guest.extension_languages(true) %}
                    {% if languages|length > 1 %}
                        <li class="nav-item">
                            <select name="lang" class="form-select js-language-selector">
                                {% for lang in languages %}
                                    <option value="{{ lang.locale }}" data-custom-properties="&lt;span class=&quot;fi fi-{{ lang.locale|split('_')[1]|lower }}&quot;&gt;&lt;/span&gt;">&nbsp;{{ lang.title }}</option>
                                {% endfor %}
                            </select>
                        </li>
                    {% endif %}

                    <!-- Dashboard Link -->
                    {% if settings.top_menu_dashboard %}
                        <li class="nav-item d-none d-md-block">
                            <a class="nav-link" href="{{ ''|link }}">{{ 'Dashboard'|trans }}</a>
                        </li>
                    {% endif %}

                    <!-- Order Link -->
                    {% if settings.top_menu_order %}
                        <li class="nav-item d-none d-md-block">
                            <a class="nav-link" href="{{ '/order'|link }}">{{ 'Order'|trans }}</a>
                        </li>
                    {% endif %}

                    <!-- Profile/Login Link -->
                    {% if settings.top_menu_profile %}
                        <li class="nav-item d-none d-md-block">
                            {% if not client %}
                                <a class="nav-link" href="{{ 'login'|link }}">{{ 'Login'|trans }}</a>
                            {% endif %}
                        </li>
                    {% endif %}

                    <!-- User Dropdown / Register Button -->
                    {% if settings.top_menu_signout %}
                        <li class="nav-item d-none d-md-block">
                            {% if client %}
                                <div class="dropdown">
                                    <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <img class="img-fluid rounded-circle" alt="{{ profile.first_name }} {{ profile.last_name }} gravatar"
                                             src="{{ profile.email|gravatar(60) }}" height="25px" width="25px">
                                        {% if profile.company %}
                                            <span>{{ profile.first_name }} {{ profile.last_name }} ({{ profile.company }})</span>
                                        {% else %}
                                            <span>{{ profile.first_name }} {{ profile.last_name }}</span>
                                        {% endif %}
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ 'client/profile'|link }}">{{ 'Profile'|trans }}</a></li>
                                        <li><a class="dropdown-item" href="{{ 'client/logout'|link }}">{{ 'Sign out'|trans }}</a></li>
                                    </ul>
                                </div>
                            {% else %}
                                <a class="ms-2 btn btn-outline-primary d-none d-md-block"
                                   href="{{ 'signup'|link }}">{{ 'Register'|trans }}</a>
                            {% endif %}
                        </li>
                    {% endif %}

                    <!-- Mobile Menu Toggle (inside collapse) -->
                    <div class="d-md-none">
                        {{ include('mobile_menu.html.twig') }}
                    </div>
                </div>
            </div>
        </nav>
    </div>
</header>



<!-- Main Content Area -->
<main class="main-content">
    {% include 'partial_pending_messages.html.twig' %}

    <div class="container" style="padding: 2rem 0;">
        {% block content_before %}{% endblock %}

        {% block content %}
            <div class="row">
                <!-- Sidebar -->
                {% if client or not settings.hide_menu %}
                    <div class="col-3 d-none d-md-block">
                        {{ include('partial_menu.html.twig') }}
                    </div>
                {% endif %}

                <!-- Main Content -->
                <div class="{% if client or not settings.hide_menu %}col-9{% else %}col-12{% endif %}">
                    {% include 'partial_message.html.twig' %}
                    <div class="content-block">
                        {% block content_body %}{% endblock %}
                    </div>
                </div>
            </div>
        {% endblock %}

        {% block content_after %}{% endblock %}
    </div>
</main>

<!-- Modern Footer -->
<footer class="footer">
    <div class="container">
        <div class="row">
            <div style="flex: 0 0 33.333%; max-width: 33.333%; margin-bottom: 1.5rem;">
                <h5 class="fw-bold mb-3">{{ guest.system_company.name }}</h5>
                <p style="color: #cbd5e1;">{{ guest.system_company.note|default('Your trusted hosting provider') }}</p>
                {% if guest.system_company.email %}
                    <p class="mb-1">
                        <i class="fas fa-envelope me-2"></i>
                        <a href="mailto:{{ guest.system_company.email }}" class="text-decoration-none" style="color: #e2e8f0;">
                            {{ guest.system_company.email }}
                        </a>
                    </p>
                {% endif %}
                {% if guest.system_company.tel %}
                    <p class="mb-0">
                        <i class="fas fa-phone me-2"></i>
                        <a href="tel:{{ guest.system_company.tel }}" class="text-decoration-none" style="color: #e2e8f0;">
                            {{ guest.system_company.tel }}
                        </a>
                    </p>
                {% endif %}
            </div>

            <div style="flex: 0 0 25%; max-width: 25%; margin-bottom: 1.5rem;">
                <h6 class="fw-bold mb-3">Services</h6>
                <ul class="list-unstyled">
                    <li><a href="{{ 'order'|link }}" class="text-decoration-none" style="color: #cbd5e1;">Order Services</a></li>
                    <li><a href="{{ 'client'|link }}" class="text-decoration-none" style="color: #cbd5e1;">Client Area</a></li>
                    <li><a href="{{ 'support'|link }}" class="text-decoration-none" style="color: #cbd5e1;">Support</a></li>
                </ul>
            </div>

            <div style="flex: 0 0 25%; max-width: 25%; margin-bottom: 1.5rem;">
                <h6 class="fw-bold mb-3">Company</h6>
                <ul class="list-unstyled">
                    <li><a href="{{ 'page/about-us'|link }}" class="text-decoration-none" style="color: #cbd5e1;">About Us</a></li>
                    <li><a href="{{ 'news'|link }}" class="text-decoration-none" style="color: #cbd5e1;">News</a></li>
                    <li><a href="{{ 'page/contact-us'|link }}" class="text-decoration-none" style="color: #cbd5e1;">Contact</a></li>
                </ul>
            </div>

            <div style="flex: 0 0 16.666%; max-width: 16.666%; margin-bottom: 1.5rem;">
                <h6 class="fw-bold mb-3">Legal</h6>
                <ul class="list-unstyled">
                    <li><a href="{{ 'tos'|link }}" class="text-decoration-none" style="color: #cbd5e1;">Terms of Service</a></li>
                    <li><a href="{{ 'privacy-policy'|link }}" class="text-decoration-none" style="color: #cbd5e1;">Privacy Policy</a></li>
                </ul>
            </div>
        </div>

        <hr style="margin: 2rem 0; border-color: #475569;">

        <div class="d-flex justify-content-between align-items-center">
            <p class="mb-0" style="color: #cbd5e1;">
                &copy; {{ "now"|date("Y") }} {{ guest.system_company.name }}. All rights reserved.
            </p>
            <p class="mb-0" style="color: #cbd5e1;">
                Powered by <a href="https://fossbilling.org" class="text-decoration-none" style="color: #e2e8f0;">FOSSBilling</a>
            </p>
        </div>
    </div>
</footer>

{% endblock %}

{{ DebugBar_render() }}

</body>
</html>

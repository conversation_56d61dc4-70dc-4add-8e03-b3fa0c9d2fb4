<!-- WIDDX Visitors Layout - Public Pages -->
<div class="public-layout">
    <!-- Public Navigation -->
    <nav class="public-navbar" id="mainNavbar">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-auto">
                    <a class="navbar-brand" href="{{ '/'|link }}">
                        {% if guest.system_company.logo_url %}
                            <img src="{{ guest.system_company.logo_url }}" alt="{{ guest.system_company.name }}" class="navbar-logo">
                        {% endif %}
                        <span>{{ guest.system_company.name }}</span>
                    </a>
                </div>
                
                <div class="col">
                    <div class="navbar-nav ms-auto d-none d-lg-flex">
                        <a class="nav-link" href="{{ '/'|link }}">{{ 'Home'|trans }}</a>
                        <a class="nav-link" href="{{ '/order'|link }}">{{ 'Services'|trans }}</a>
                        <a class="nav-link" href="{{ '/page/pricing'|link }}">{{ 'Pricing'|trans }}</a>
                        
                        {% if guest.support_kb_enabled() %}
                            <a class="nav-link" href="{{ '/support/kb'|link }}">{{ 'Knowledge Base'|trans }}</a>
                        {% endif %}
                        
                        {% if guest.extension_is_on({"mod":"support"}) %}
                            <a class="nav-link" href="{{ '/support'|link }}">{{ 'Support'|trans }}</a>
                        {% endif %}
                        
                        <a class="nav-link" href="{{ '/contact'|link }}">{{ 'Contact'|trans }}</a>
                    </div>
                </div>
                
                <div class="col-auto">
                    <div class="navbar-actions d-flex align-items-center gap-3">
                        {% if client %}
                            <a href="{{ '/client'|link }}" class="btn btn-outline-primary">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                {{ 'Dashboard'|trans }}
                            </a>
                            <a href="{{ '/client/logout'|link }}" class="btn btn-secondary">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                {{ 'Logout'|trans }}
                            </a>
                        {% else %}
                            {% if settings.show_signup_link %}
                                <a href="{{ '/client/register'|link }}" class="btn btn-outline-primary">
                                    {{ 'Sign Up'|trans }}
                                </a>
                            {% endif %}
                            <a href="{{ '/client/login'|link }}" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                {{ 'Login'|trans }}
                            </a>
                        {% endif %}
                        
                        <!-- Mobile Menu Toggle -->
                        <button class="navbar-toggler d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#mobileNav">
                            <span class="navbar-toggler-icon">
                                <i class="fas fa-bars"></i>
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Mobile Navigation -->
            <div class="collapse navbar-collapse d-lg-none mt-3" id="mobileNav">
                <div class="navbar-nav">
                    <a class="nav-link" href="{{ '/'|link }}">{{ 'Home'|trans }}</a>
                    <a class="nav-link" href="{{ '/order'|link }}">{{ 'Services'|trans }}</a>
                    <a class="nav-link" href="{{ '/page/pricing'|link }}">{{ 'Pricing'|trans }}</a>
                    
                    {% if guest.support_kb_enabled() %}
                        <a class="nav-link" href="{{ '/support/kb'|link }}">{{ 'Knowledge Base'|trans }}</a>
                    {% endif %}
                    
                    {% if guest.extension_is_on({"mod":"support"}) %}
                        <a class="nav-link" href="{{ '/support'|link }}">{{ 'Support'|trans }}</a>
                    {% endif %}
                    
                    <a class="nav-link" href="{{ '/contact'|link }}">{{ 'Contact'|trans }}</a>
                    
                    <hr class="my-3">
                    
                    {% if not client %}
                        {% if settings.show_signup_link %}
                            <a class="nav-link" href="{{ '/client/register'|link }}">{{ 'Sign Up'|trans }}</a>
                        {% endif %}
                        <a class="nav-link" href="{{ '/client/login'|link }}">{{ 'Login'|trans }}</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content Area -->
    <main class="main-content">
        <!-- Breadcrumb (if enabled and not on homepage) -->
        {% if settings.show_breadcrumb and request.uri != '/' %}
            <div class="breadcrumb-section bg-light py-3">
                <div class="container">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item">
                                <a href="{{ '/'|link }}">
                                    <i class="fas fa-home me-1"></i>
                                    {{ 'Home'|trans }}
                                </a>
                            </li>
                            {% block breadcrumb %}{% endblock %}
                        </ol>
                    </nav>
                </div>
            </div>
        {% endif %}

        <!-- Page Content -->
        {% block content %}
            <!-- Default content will be replaced by specific page templates -->
        {% endblock %}
    </main>

    <!-- Public Footer -->
    {% if settings.footer_enabled %}
        <footer class="public-footer">
            <div class="container">
                <div class="row">
                    <!-- Company Information -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <h5 class="mb-4">{{ guest.system_company.name }}</h5>
                        {% if guest.system_company.address_1 %}
                            <p class="mb-2">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                {{ guest.system_company.address_1 }}
                                {% if guest.system_company.address_2 %}
                                    <br>{{ guest.system_company.address_2 }}
                                {% endif %}
                                <br>{{ guest.system_company.city }}, {{ guest.system_company.state }} {{ guest.system_company.postcode }}
                                <br>{{ guest.system_company.country }}
                            </p>
                        {% endif %}
                        
                        {% if guest.system_company.tel %}
                            <p class="mb-2">
                                <i class="fas fa-phone me-2"></i>
                                <a href="tel:{{ guest.system_company.tel }}">{{ guest.system_company.tel }}</a>
                            </p>
                        {% endif %}
                        
                        {% if guest.system_company.email %}
                            <p class="mb-2">
                                <i class="fas fa-envelope me-2"></i>
                                <a href="mailto:{{ guest.system_company.email }}">{{ guest.system_company.email }}</a>
                            </p>
                        {% endif %}
                    </div>
                    
                    <!-- Quick Links -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <h6 class="mb-4">{{ 'Quick Links'|trans }}</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2"><a href="{{ '/'|link }}">{{ 'Home'|trans }}</a></li>
                            <li class="mb-2"><a href="{{ '/order'|link }}">{{ 'Services'|trans }}</a></li>
                            <li class="mb-2"><a href="{{ '/page/pricing'|link }}">{{ 'Pricing'|trans }}</a></li>
                            {% if guest.support_kb_enabled() %}
                                <li class="mb-2"><a href="{{ '/support/kb'|link }}">{{ 'Knowledge Base'|trans }}</a></li>
                            {% endif %}
                            <li class="mb-2"><a href="{{ '/contact'|link }}">{{ 'Contact'|trans }}</a></li>
                        </ul>
                    </div>
                    
                    <!-- Support -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <h6 class="mb-4">{{ 'Support'|trans }}</h6>
                        <ul class="list-unstyled">
                            {% if guest.extension_is_on({"mod":"support"}) %}
                                <li class="mb-2"><a href="{{ '/support'|link }}">{{ 'Support Center'|trans }}</a></li>
                            {% endif %}
                            {% if guest.support_kb_enabled() %}
                                <li class="mb-2"><a href="{{ '/support/kb'|link }}">{{ 'Knowledge Base'|trans }}</a></li>
                            {% endif %}
                            <li class="mb-2"><a href="{{ '/client/login'|link }}">{{ 'Client Login'|trans }}</a></li>
                            {% if settings.show_signup_link %}
                                <li class="mb-2"><a href="{{ '/client/register'|link }}">{{ 'Sign Up'|trans }}</a></li>
                            {% endif %}
                        </ul>
                    </div>
                    
                    <!-- Custom Footer Links -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <h6 class="mb-4">{{ 'Legal'|trans }}</h6>
                        <ul class="list-unstyled">
                            {% if settings.footer_link_1_title and settings.footer_link_1_page %}
                                <li class="mb-2"><a href="{{ settings.footer_link_1_page|link }}">{{ settings.footer_link_1_title }}</a></li>
                            {% endif %}
                            {% if settings.footer_link_2_title and settings.footer_link_2_page %}
                                <li class="mb-2"><a href="{{ settings.footer_link_2_page|link }}">{{ settings.footer_link_2_title }}</a></li>
                            {% endif %}
                            {% if settings.footer_link_3_title and settings.footer_link_3_page %}
                                <li class="mb-2"><a href="{{ settings.footer_link_3_page|link }}">{{ settings.footer_link_3_title }}</a></li>
                            {% endif %}
                            {% if settings.footer_link_4_title and settings.footer_link_4_page %}
                                <li class="mb-2"><a href="{{ settings.footer_link_4_page|link }}">{{ settings.footer_link_4_title }}</a></li>
                            {% endif %}
                            {% if settings.footer_link_5_title and settings.footer_link_5_page %}
                                <li class="mb-2"><a href="{{ settings.footer_link_5_page|link }}">{{ settings.footer_link_5_title }}</a></li>
                            {% endif %}
                        </ul>
                    </div>
                    
                    <!-- Social Media & Newsletter -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <h6 class="mb-4">{{ 'Connect'|trans }}</h6>
                        
                        <!-- Social Media Links -->
                        <div class="social-links mb-4">
                            {% if guest.system_company.facebook %}
                                <a href="{{ guest.system_company.facebook }}" target="_blank" class="me-3">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                            {% endif %}
                            {% if guest.system_company.twitter %}
                                <a href="{{ guest.system_company.twitter }}" target="_blank" class="me-3">
                                    <i class="fab fa-twitter"></i>
                                </a>
                            {% endif %}
                            {% if guest.system_company.linkedin %}
                                <a href="{{ guest.system_company.linkedin }}" target="_blank" class="me-3">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                            {% endif %}
                            {% if guest.system_company.instagram %}
                                <a href="{{ guest.system_company.instagram }}" target="_blank" class="me-3">
                                    <i class="fab fa-instagram"></i>
                                </a>
                            {% endif %}
                        </div>
                        
                        <!-- Newsletter Signup -->
                        <div class="newsletter-signup">
                            <p class="small mb-2">{{ 'Stay updated with our latest news'|trans }}</p>
                            <form class="d-flex">
                                <input type="email" class="form-control form-control-sm me-2" placeholder="{{ 'Your email'|trans }}">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Footer Bottom -->
                <hr class="footer-divider">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="mb-0">
                            &copy; {{ "now"|date("Y") }} {{ guest.system_company.name }}. {{ 'All rights reserved.'|trans }}
                            {% if settings.footer_signature %}
                                <br>{{ settings.footer_signature|raw }}
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="mb-0">
                            {{ 'Powered by'|trans }} 
                            <a href="https://fossbilling.org" target="_blank" class="text-decoration-none">FOSSBilling</a>
                            {{ 'with'|trans }}
                            <a href="https://widdx.com" target="_blank" class="text-decoration-none">WIDDX Theme</a>
                        </p>
                        
                        {% if settings.footer_to_top_enabled %}
                            <button class="btn btn-sm btn-outline-light mt-2" id="backToTop">
                                <i class="fas fa-arrow-up me-1"></i>
                                {{ 'Back to Top'|trans }}
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </footer>
    {% endif %}
</div>

<!-- Back to Top Functionality -->
{% if settings.footer_to_top_enabled %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const backToTopBtn = document.getElementById('backToTop');
            if (backToTopBtn) {
                backToTopBtn.addEventListener('click', function() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }
        });
    </script>
{% endif %}

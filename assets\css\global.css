/* WIDDX Global CSS Framework */
/* Base styles, variables, typography, and common components */

/* Import WIDDX Design System Variables */
:root {
  /* WIDDX Brand Colors */
  --widdx-royal-blue: #1E40AF;
  --widdx-royal-blue-light: #3B82F6;
  --widdx-royal-blue-dark: #1E3A8A;
  --widdx-charcoal: #374151;
  --widdx-charcoal-light: #4B5563;
  --widdx-charcoal-dark: #1F2937;
  --widdx-silver: #9CA3AF;
  --widdx-silver-light: #D1D5DB;
  --widdx-silver-dark: #6B7280;
  --widdx-violet: #7C3AED;
  --widdx-violet-light: #8B5CF6;
  --widdx-violet-dark: #6D28D9;
  
  /* Semantic Colors */
  --widdx-primary: var(--widdx-royal-blue);
  --widdx-primary-light: var(--widdx-royal-blue-light);
  --widdx-primary-dark: var(--widdx-royal-blue-dark);
  --widdx-secondary: var(--widdx-charcoal);
  --widdx-accent: var(--widdx-violet);
  
  /* Neutral Colors */
  --widdx-white: #FFFFFF;
  --widdx-gray-50: #F9FAFB;
  --widdx-gray-100: #F3F4F6;
  --widdx-gray-200: #E5E7EB;
  --widdx-gray-300: #D1D5DB;
  --widdx-gray-400: #9CA3AF;
  --widdx-gray-500: #6B7280;
  --widdx-gray-600: #4B5563;
  --widdx-gray-700: #374151;
  --widdx-gray-800: #1F2937;
  --widdx-gray-900: #111827;
  
  /* Status Colors */
  --widdx-success: #10B981;
  --widdx-warning: #F59E0B;
  --widdx-error: #EF4444;
  --widdx-info: #06B6D4;
  
  /* Typography */
  --widdx-font-family: 'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --widdx-font-size-xs: 0.75rem;
  --widdx-font-size-sm: 0.875rem;
  --widdx-font-size-base: 1rem;
  --widdx-font-size-lg: 1.125rem;
  --widdx-font-size-xl: 1.25rem;
  --widdx-font-size-2xl: 1.5rem;
  --widdx-font-size-3xl: 1.875rem;
  --widdx-font-size-4xl: 2.25rem;
  --widdx-font-size-5xl: 3rem;
  
  /* Spacing */
  --widdx-spacing-1: 0.25rem;
  --widdx-spacing-2: 0.5rem;
  --widdx-spacing-3: 0.75rem;
  --widdx-spacing-4: 1rem;
  --widdx-spacing-5: 1.25rem;
  --widdx-spacing-6: 1.5rem;
  --widdx-spacing-8: 2rem;
  --widdx-spacing-10: 2.5rem;
  --widdx-spacing-12: 3rem;
  --widdx-spacing-16: 4rem;
  --widdx-spacing-20: 5rem;
  
  /* Border Radius */
  --widdx-radius-sm: 0.375rem;
  --widdx-radius-md: 0.5rem;
  --widdx-radius-lg: 0.75rem;
  --widdx-radius-xl: 1rem;
  --widdx-radius-full: 9999px;
  
  /* Shadows */
  --widdx-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --widdx-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --widdx-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --widdx-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --widdx-transition-fast: 150ms ease-in-out;
  --widdx-transition-normal: 250ms ease-in-out;
  --widdx-transition-slow: 350ms ease-in-out;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--widdx-font-family);
  font-size: var(--widdx-font-size-base);
  line-height: 1.6;
  color: var(--widdx-gray-800);
  background-color: var(--widdx-gray-50);
  font-feature-settings: "kern" 1, "liga" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography System */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: var(--widdx-spacing-4);
  color: var(--widdx-gray-900);
}

h1 { 
  font-size: var(--widdx-font-size-5xl);
  font-weight: 700;
}

h2 { 
  font-size: var(--widdx-font-size-4xl);
  font-weight: 600;
}

h3 { 
  font-size: var(--widdx-font-size-3xl);
}

h4 { 
  font-size: var(--widdx-font-size-2xl);
}

h5 { 
  font-size: var(--widdx-font-size-xl);
}

h6 { 
  font-size: var(--widdx-font-size-lg);
}

p {
  margin-bottom: var(--widdx-spacing-4);
  color: var(--widdx-gray-700);
}

a {
  color: var(--widdx-primary);
  text-decoration: none;
  transition: color var(--widdx-transition-fast);
}

a:hover {
  color: var(--widdx-primary-dark);
  text-decoration: underline;
}

/* Layout System */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--widdx-spacing-4);
}

.container-fluid {
  width: 100%;
  padding: 0 var(--widdx-spacing-4);
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(-1 * var(--widdx-spacing-3));
}

.col {
  flex: 1;
  padding: 0 var(--widdx-spacing-3);
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* Button System */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--widdx-spacing-3) var(--widdx-spacing-6);
  font-size: var(--widdx-font-size-base);
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--widdx-radius-md);
  cursor: pointer;
  transition: all var(--widdx-transition-fast);
  background: none;
  user-select: none;
}

.btn:hover {
  transform: translateY(-1px);
  text-decoration: none;
  box-shadow: var(--widdx-shadow-md);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  color: var(--widdx-white);
  background-color: var(--widdx-primary);
  border-color: var(--widdx-primary);
}

.btn-primary:hover {
  background-color: var(--widdx-primary-dark);
  border-color: var(--widdx-primary-dark);
  color: var(--widdx-white);
}

.btn-secondary {
  color: var(--widdx-white);
  background-color: var(--widdx-secondary);
  border-color: var(--widdx-secondary);
}

.btn-secondary:hover {
  background-color: var(--widdx-charcoal-dark);
  border-color: var(--widdx-charcoal-dark);
  color: var(--widdx-white);
}

.btn-outline-primary {
  color: var(--widdx-primary);
  border-color: var(--widdx-primary);
  background-color: transparent;
}

.btn-outline-primary:hover {
  color: var(--widdx-white);
  background-color: var(--widdx-primary);
  border-color: var(--widdx-primary);
}

.btn-lg {
  padding: var(--widdx-spacing-4) var(--widdx-spacing-8);
  font-size: var(--widdx-font-size-lg);
}

.btn-sm {
  padding: var(--widdx-spacing-2) var(--widdx-spacing-4);
  font-size: var(--widdx-font-size-sm);
}

/* Card System */
.card {
  background: var(--widdx-white);
  border-radius: var(--widdx-radius-lg);
  box-shadow: var(--widdx-shadow-sm);
  border: 1px solid var(--widdx-gray-200);
  transition: all var(--widdx-transition-normal);
  margin-bottom: var(--widdx-spacing-6);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--widdx-shadow-lg);
}

.card-header {
  padding: var(--widdx-spacing-6);
  border-bottom: 1px solid var(--widdx-gray-200);
  background: linear-gradient(135deg, var(--widdx-gray-50), var(--widdx-gray-100));
  font-weight: 600;
  color: var(--widdx-gray-900);
}

.card-body {
  padding: var(--widdx-spacing-6);
}

.card-footer {
  padding: var(--widdx-spacing-6);
  border-top: 1px solid var(--widdx-gray-200);
  background: var(--widdx-gray-50);
}

/* Form System */
.form-group {
  margin-bottom: var(--widdx-spacing-6);
}

.form-label {
  display: block;
  margin-bottom: var(--widdx-spacing-2);
  font-weight: 500;
  color: var(--widdx-gray-700);
  font-size: var(--widdx-font-size-sm);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--widdx-spacing-3) var(--widdx-spacing-4);
  font-size: var(--widdx-font-size-base);
  line-height: 1.5;
  color: var(--widdx-gray-800);
  background-color: var(--widdx-white);
  border: 1px solid var(--widdx-gray-300);
  border-radius: var(--widdx-radius-md);
  transition: all var(--widdx-transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--widdx-primary);
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.form-select {
  display: block;
  width: 100%;
  padding: var(--widdx-spacing-3) calc(var(--widdx-spacing-10) + var(--widdx-spacing-4)) var(--widdx-spacing-3) var(--widdx-spacing-4);
  font-size: var(--widdx-font-size-base);
  line-height: 1.5;
  color: var(--widdx-gray-800);
  background-color: var(--widdx-white);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--widdx-spacing-3) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  border: 1px solid var(--widdx-gray-300);
  border-radius: var(--widdx-radius-md);
  transition: all var(--widdx-transition-fast);
  appearance: none;
}

.form-select:focus {
  outline: none;
  border-color: var(--widdx-primary);
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-start { justify-content: flex-start; }
.justify-content-end { justify-content: flex-end; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }
.min-vh-100 { min-height: 100vh; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--widdx-spacing-1); }
.mb-2 { margin-bottom: var(--widdx-spacing-2); }
.mb-3 { margin-bottom: var(--widdx-spacing-3); }
.mb-4 { margin-bottom: var(--widdx-spacing-4); }
.mb-5 { margin-bottom: var(--widdx-spacing-5); }
.mb-6 { margin-bottom: var(--widdx-spacing-6); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--widdx-spacing-1); }
.mt-2 { margin-top: var(--widdx-spacing-2); }
.mt-3 { margin-top: var(--widdx-spacing-3); }
.mt-4 { margin-top: var(--widdx-spacing-4); }
.mt-5 { margin-top: var(--widdx-spacing-5); }
.mt-6 { margin-top: var(--widdx-spacing-6); }

.me-1 { margin-right: var(--widdx-spacing-1); }
.me-2 { margin-right: var(--widdx-spacing-2); }
.me-3 { margin-right: var(--widdx-spacing-3); }
.me-4 { margin-right: var(--widdx-spacing-4); }

.ms-1 { margin-left: var(--widdx-spacing-1); }
.ms-2 { margin-left: var(--widdx-spacing-2); }
.ms-3 { margin-left: var(--widdx-spacing-3); }
.ms-4 { margin-left: var(--widdx-spacing-4); }

.p-0 { padding: 0; }
.p-1 { padding: var(--widdx-spacing-1); }
.p-2 { padding: var(--widdx-spacing-2); }
.p-3 { padding: var(--widdx-spacing-3); }
.p-4 { padding: var(--widdx-spacing-4); }
.p-5 { padding: var(--widdx-spacing-5); }
.p-6 { padding: var(--widdx-spacing-6); }

/* Text Utilities */
.text-gradient {
  background: linear-gradient(135deg, var(--widdx-primary), var(--widdx-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-primary { color: var(--widdx-primary); }
.text-secondary { color: var(--widdx-secondary); }
.text-success { color: var(--widdx-success); }
.text-warning { color: var(--widdx-warning); }
.text-error { color: var(--widdx-error); }
.text-info { color: var(--widdx-info); }
.text-muted { color: var(--widdx-gray-500); }

/* Animation Utilities */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out forwards;
}

.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
  animation: fadeInRight 0.8s ease-out forwards;
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
  animation: fadeInLeft 0.8s ease-out forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design System */
@media (max-width: 1024px) {
  .container {
    max-width: 100%;
    padding: 0 var(--widdx-spacing-6);
  }

  h1 { font-size: var(--widdx-font-size-4xl); }
  h2 { font-size: var(--widdx-font-size-3xl); }
  h3 { font-size: var(--widdx-font-size-2xl); }
  h4 { font-size: var(--widdx-font-size-xl); }

  .btn-lg {
    padding: var(--widdx-spacing-3) var(--widdx-spacing-6);
    font-size: var(--widdx-font-size-base);
  }

  .card-header,
  .card-body,
  .card-footer {
    padding: var(--widdx-spacing-4);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--widdx-spacing-4);
  }

  .row {
    margin: 0 calc(-1 * var(--widdx-spacing-2));
  }

  .col,
  .col-1, .col-2, .col-3, .col-4, .col-6, .col-8, .col-9, .col-12 {
    padding: 0 var(--widdx-spacing-2);
  }

  /* Mobile column system */
  .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-6, .col-md-8, .col-md-9, .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .col-md-6.col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  h1 { font-size: var(--widdx-font-size-3xl); }
  h2 { font-size: var(--widdx-font-size-2xl); }
  h3 { font-size: var(--widdx-font-size-xl); }
  h4 { font-size: var(--widdx-font-size-lg); }

  .btn {
    padding: var(--widdx-spacing-3) var(--widdx-spacing-4);
    font-size: var(--widdx-font-size-sm);
  }

  .btn-lg {
    padding: var(--widdx-spacing-3) var(--widdx-spacing-5);
    font-size: var(--widdx-font-size-base);
  }

  .card-header,
  .card-body,
  .card-footer {
    padding: var(--widdx-spacing-3);
  }

  .form-group {
    margin-bottom: var(--widdx-spacing-4);
  }

  .form-control,
  .form-select {
    padding: var(--widdx-spacing-2) var(--widdx-spacing-3);
    font-size: var(--widdx-font-size-sm);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--widdx-spacing-3);
  }

  h1 { font-size: var(--widdx-font-size-2xl); }
  h2 { font-size: var(--widdx-font-size-xl); }
  h3 { font-size: var(--widdx-font-size-lg); }
  h4 { font-size: var(--widdx-font-size-base); }

  .btn {
    padding: var(--widdx-spacing-2) var(--widdx-spacing-3);
    font-size: var(--widdx-font-size-xs);
  }

  .btn-lg {
    padding: var(--widdx-spacing-3) var(--widdx-spacing-4);
    font-size: var(--widdx-font-size-sm);
  }

  .card-header,
  .card-body,
  .card-footer {
    padding: var(--widdx-spacing-2);
  }
}

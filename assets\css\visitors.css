/* WIDDX Visitors CSS - Public Pages Styling */
/* Landing page, features, pricing, and other public-facing pages */

/* Hero Section Styles */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--widdx-primary) 0%, var(--widdx-accent) 100%);
  color: var(--widdx-white);
  overflow: hidden;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--widdx-spacing-2) var(--widdx-spacing-4);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--widdx-radius-full);
  font-size: var(--widdx-font-size-sm);
  font-weight: 500;
  margin-bottom: var(--widdx-spacing-6);
  backdrop-filter: blur(10px);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--widdx-spacing-8) var(--widdx-spacing-4);
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  margin-bottom: var(--widdx-spacing-6);
  line-height: 1.2;
  color: var(--widdx-white);
}

.hero-subtitle {
  font-size: clamp(1.125rem, 2.5vw, 1.5rem);
  margin-bottom: var(--widdx-spacing-8);
  opacity: 0.9;
  line-height: 1.6;
  color: var(--widdx-white);
}

.hero-cta {
  display: flex;
  gap: var(--widdx-spacing-4);
  justify-content: center;
  flex-wrap: wrap;
  margin-top: var(--widdx-spacing-8);
}

.hero-cta .btn {
  padding: var(--widdx-spacing-4) var(--widdx-spacing-8);
  font-size: var(--widdx-font-size-lg);
  font-weight: 600;
  border-radius: var(--widdx-radius-lg);
  transition: all var(--widdx-transition-normal);
}

.hero-cta .btn-primary {
  background: var(--widdx-white);
  color: var(--widdx-primary);
  border: 2px solid var(--widdx-white);
}

.hero-cta .btn-primary:hover {
  background: transparent;
  color: var(--widdx-white);
  border-color: var(--widdx-white);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.hero-cta .btn-outline {
  background: transparent;
  color: var(--widdx-white);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.hero-cta .btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--widdx-white);
  transform: translateY(-2px);
}

/* Public Navigation Styles */
.public-navbar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: transparent;
  transition: all var(--widdx-transition-normal);
  padding: var(--widdx-spacing-4) 0;
}

.public-navbar.scrolled {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--widdx-shadow-md);
}

.public-navbar.scrolled .navbar-brand,
.public-navbar.scrolled .nav-link {
  color: var(--widdx-gray-800) !important;
}

.public-navbar .navbar-brand {
  font-size: var(--widdx-font-size-xl);
  font-weight: 700;
  color: var(--widdx-white);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--widdx-spacing-3);
}

.public-navbar .navbar-logo {
  height: 40px;
  width: auto;
}

.public-navbar .nav-link {
  color: var(--widdx-white);
  font-weight: 500;
  padding: var(--widdx-spacing-2) var(--widdx-spacing-4);
  border-radius: var(--widdx-radius-md);
  transition: all var(--widdx-transition-fast);
  text-decoration: none;
}

.public-navbar .nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--widdx-white);
  text-decoration: none;
}

.public-navbar .btn {
  margin-left: var(--widdx-spacing-2);
}

/* Features Section */
.features-section {
  padding: var(--widdx-spacing-20) 0;
  background: var(--widdx-white);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--widdx-spacing-8);
  margin-top: var(--widdx-spacing-12);
}

.feature-card {
  text-align: center;
  padding: var(--widdx-spacing-8);
  border-radius: var(--widdx-radius-xl);
  background: var(--widdx-white);
  border: 1px solid var(--widdx-gray-200);
  transition: all var(--widdx-transition-normal);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--widdx-primary), var(--widdx-accent));
  transform: scaleX(0);
  transition: transform var(--widdx-transition-normal);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--widdx-shadow-xl);
  border-color: var(--widdx-primary);
}

.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--widdx-spacing-6);
  background: linear-gradient(135deg, var(--widdx-primary), var(--widdx-accent));
  border-radius: var(--widdx-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--widdx-font-size-2xl);
  color: var(--widdx-white);
  transition: all var(--widdx-transition-normal);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}

.feature-title {
  font-size: var(--widdx-font-size-xl);
  font-weight: 600;
  margin-bottom: var(--widdx-spacing-4);
  color: var(--widdx-gray-900);
}

.feature-description {
  color: var(--widdx-gray-600);
  line-height: 1.7;
}

/* Pricing Section */
.pricing-section {
  padding: var(--widdx-spacing-20) 0;
  background: linear-gradient(135deg, var(--widdx-gray-50), var(--widdx-gray-100));
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--widdx-spacing-8);
  margin-top: var(--widdx-spacing-12);
}

.pricing-card {
  background: var(--widdx-white);
  border-radius: var(--widdx-radius-xl);
  padding: var(--widdx-spacing-8);
  text-align: center;
  border: 2px solid var(--widdx-gray-200);
  transition: all var(--widdx-transition-normal);
  position: relative;
  overflow: hidden;
}

.pricing-card.featured {
  border-color: var(--widdx-primary);
  transform: scale(1.05);
  box-shadow: var(--widdx-shadow-xl);
}

.pricing-card.featured::before {
  content: 'Most Popular';
  position: absolute;
  top: var(--widdx-spacing-4);
  left: 50%;
  transform: translateX(-50%);
  background: var(--widdx-primary);
  color: var(--widdx-white);
  padding: var(--widdx-spacing-1) var(--widdx-spacing-4);
  border-radius: var(--widdx-radius-full);
  font-size: var(--widdx-font-size-sm);
  font-weight: 600;
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--widdx-shadow-lg);
  border-color: var(--widdx-primary);
}

.pricing-card.featured:hover {
  transform: scale(1.05) translateY(-4px);
}

.pricing-title {
  font-size: var(--widdx-font-size-xl);
  font-weight: 600;
  margin-bottom: var(--widdx-spacing-4);
  color: var(--widdx-gray-900);
}

.pricing-price {
  font-size: var(--widdx-font-size-4xl);
  font-weight: 700;
  color: var(--widdx-primary);
  margin-bottom: var(--widdx-spacing-2);
}

.pricing-period {
  color: var(--widdx-gray-500);
  margin-bottom: var(--widdx-spacing-6);
}

.pricing-features {
  list-style: none;
  padding: 0;
  margin: var(--widdx-spacing-6) 0;
}

.pricing-features li {
  padding: var(--widdx-spacing-2) 0;
  color: var(--widdx-gray-600);
  display: flex;
  align-items: center;
  gap: var(--widdx-spacing-2);
}

.pricing-features li::before {
  content: '✓';
  color: var(--widdx-success);
  font-weight: bold;
  font-size: var(--widdx-font-size-lg);
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: var(--widdx-spacing-12);
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  margin-bottom: var(--widdx-spacing-4);
  color: var(--widdx-gray-900);
}

.section-subtitle {
  font-size: var(--widdx-font-size-xl);
  color: var(--widdx-gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Public Footer */
.public-footer {
  background: linear-gradient(135deg, var(--widdx-gray-900), var(--widdx-gray-800));
  color: var(--widdx-gray-300);
  padding: var(--widdx-spacing-16) 0 var(--widdx-spacing-8);
}

.public-footer h5,
.public-footer h6 {
  color: var(--widdx-white);
  margin-bottom: var(--widdx-spacing-4);
}

.public-footer a {
  color: var(--widdx-gray-300);
  transition: all var(--widdx-transition-fast);
  text-decoration: none;
}

.public-footer a:hover {
  color: var(--widdx-white);
  transform: translateX(2px);
  text-decoration: none;
}

.public-footer .footer-divider {
  border-color: var(--widdx-gray-700);
  margin: var(--widdx-spacing-8) 0;
}

/* Animation Classes */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  transition: all var(--widdx-transition-slow);
}

.fade-in-up.animate {
  opacity: 1;
  transform: translateY(0);
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
  transition: all var(--widdx-transition-slow);
}

.fade-in-left.animate {
  opacity: 1;
  transform: translateX(0);
}

.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
  transition: all var(--widdx-transition-slow);
}

.fade-in-right.animate {
  opacity: 1;
  transform: translateX(0);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
  transition: all var(--widdx-transition-slow);
}

.scale-in.animate {
  opacity: 1;
  transform: scale(1);
}

/* Hero Stats */
.hero-stats {
  margin-top: var(--widdx-spacing-12);
}

.hero-stat {
  text-align: center;
  padding: var(--widdx-spacing-4);
}

.hero-stat-number {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  color: var(--widdx-white);
  display: block;
  margin-bottom: var(--widdx-spacing-2);
}

.hero-stat-label {
  font-size: var(--widdx-font-size-sm);
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

/* Trust Indicators */
.hero-trust {
  margin-top: var(--widdx-spacing-6);
}

.trust-text {
  font-size: var(--widdx-font-size-sm);
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: var(--widdx-spacing-3);
  text-align: center;
}

.trust-logos {
  display: flex;
  justify-content: center;
  gap: var(--widdx-spacing-6);
  flex-wrap: wrap;
}

.trust-logo {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--widdx-radius-lg);
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--widdx-font-size-xl);
  transition: all var(--widdx-transition-normal);
  backdrop-filter: blur(10px);
}

.trust-logo:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--widdx-white);
  transform: translateY(-4px);
}

/* Dashboard Preview */
.hero-visual {
  position: relative;
  padding: var(--widdx-spacing-8);
}

.dashboard-preview {
  background: var(--widdx-white);
  border-radius: var(--widdx-radius-xl);
  box-shadow: var(--widdx-shadow-2xl);
  overflow: hidden;
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
  transition: all var(--widdx-transition-normal);
}

.dashboard-preview:hover {
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

.dashboard-header {
  background: var(--widdx-gray-50);
  padding: var(--widdx-spacing-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--widdx-gray-200);
}

.dashboard-nav {
  display: flex;
  gap: var(--widdx-spacing-2);
}

.nav-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--widdx-gray-300);
  transition: all var(--widdx-transition-fast);
}

.nav-dot.active {
  background: var(--widdx-primary);
}

.dashboard-title {
  font-size: var(--widdx-font-size-sm);
  font-weight: 600;
  color: var(--widdx-gray-700);
}

.dashboard-content {
  padding: var(--widdx-spacing-6);
}

.status-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--widdx-spacing-4);
  margin-bottom: var(--widdx-spacing-6);
}

.status-card {
  background: var(--widdx-gray-50);
  border-radius: var(--widdx-radius-lg);
  padding: var(--widdx-spacing-4);
  display: flex;
  align-items: center;
  gap: var(--widdx-spacing-3);
  position: relative;
}

.status-icon {
  width: 40px;
  height: 40px;
  background: var(--widdx-primary);
  border-radius: var(--widdx-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--widdx-white);
  font-size: var(--widdx-font-size-lg);
}

.status-info {
  flex: 1;
}

.status-label {
  font-size: var(--widdx-font-size-xs);
  color: var(--widdx-gray-500);
  margin-bottom: var(--widdx-spacing-1);
}

.status-value {
  font-size: var(--widdx-font-size-sm);
  font-weight: 600;
  color: var(--widdx-gray-900);
}

.status-value.online {
  color: var(--widdx-success);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: absolute;
  top: var(--widdx-spacing-2);
  right: var(--widdx-spacing-2);
}

.status-indicator.online {
  background: var(--widdx-success);
  animation: pulse-green 2s infinite;
}

.status-indicator.excellent {
  background: var(--widdx-primary);
  animation: pulse-blue 2s infinite;
}

@keyframes pulse-green {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes pulse-blue {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Usage Chart */
.usage-chart {
  margin-bottom: var(--widdx-spacing-6);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--widdx-spacing-4);
}

.chart-header h4 {
  font-size: var(--widdx-font-size-base);
  font-weight: 600;
  color: var(--widdx-gray-900);
  margin: 0;
}

.chart-period {
  font-size: var(--widdx-font-size-xs);
  color: var(--widdx-gray-500);
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: var(--widdx-spacing-2);
  height: 80px;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, var(--widdx-primary), var(--widdx-accent));
  border-radius: var(--widdx-radius-sm) var(--widdx-radius-sm) 0 0;
  min-height: 20%;
  animation: chartGrow 1s ease-out;
  animation-delay: calc(var(--i, 0) * 0.1s);
  animation-fill-mode: both;
  transform: scaleY(0);
}

@keyframes chartGrow {
  to { transform: scaleY(1); }
}

/* Quick Actions */
.quick-actions {
  display: flex;
  gap: var(--widdx-spacing-2);
}

.action-btn {
  flex: 1;
  background: var(--widdx-gray-100);
  border: none;
  border-radius: var(--widdx-radius-md);
  padding: var(--widdx-spacing-3);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--widdx-spacing-1);
  font-size: var(--widdx-font-size-xs);
  color: var(--widdx-gray-700);
  transition: all var(--widdx-transition-fast);
  cursor: pointer;
}

.action-btn:hover {
  background: var(--widdx-primary);
  color: var(--widdx-white);
  transform: translateY(-2px);
}

.action-btn i {
  font-size: var(--widdx-font-size-base);
}

/* Floating Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--widdx-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--widdx-font-size-xl);
  backdrop-filter: blur(10px);
  animation: float 6s ease-in-out infinite;
}

.floating-element.element-1 {
  top: 20%;
  right: 10%;
  animation-delay: 0s;
}

.floating-element.element-2 {
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.floating-element.element-3 {
  top: 40%;
  right: 5%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* Hero Background */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--widdx-primary) 0%, var(--widdx-accent) 100%);
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
  animation: patternMove 20s linear infinite;
}

@keyframes patternMove {
  0% { background-position: 0 0, 50px 50px; }
  100% { background-position: 100px 100px, 150px 150px; }
}

.bg-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: particleFloat 8s linear infinite;
}

.particle:nth-child(1) {
  left: 10%;
  animation-delay: 0s;
}

.particle:nth-child(2) {
  left: 30%;
  animation-delay: 2s;
}

.particle:nth-child(3) {
  left: 50%;
  animation-delay: 4s;
}

.particle:nth-child(4) {
  left: 70%;
  animation-delay: 6s;
}

.particle:nth-child(5) {
  left: 90%;
  animation-delay: 8s;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) scale(1);
    opacity: 0;
  }
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: var(--widdx-spacing-8);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--widdx-spacing-2);
  color: rgba(255, 255, 255, 0.8);
  animation: bounce 2s infinite;
}

.scroll-arrow {
  width: 40px;
  height: 40px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--widdx-font-size-lg);
}

.scroll-text {
  font-size: var(--widdx-font-size-xs);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
  40% { transform: translateX(-50%) translateY(-10px); }
  60% { transform: translateX(-50%) translateY(-5px); }
}

/* Responsive Design - Tablet */
@media (max-width: 1024px) {
  .hero-section {
    min-height: 80vh;
    padding: var(--widdx-spacing-8) 0;
  }

  .hero-title {
    font-size: var(--widdx-font-size-4xl);
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: var(--widdx-font-size-lg);
  }

  .hero-visual {
    padding: var(--widdx-spacing-6);
    margin-top: var(--widdx-spacing-8);
  }

  .dashboard-preview {
    transform: perspective(800px) rotateY(-3deg) rotateX(3deg);
  }

  .trust-logos {
    gap: var(--widdx-spacing-4);
  }

  .trust-logo {
    width: 40px;
    height: 40px;
    font-size: var(--widdx-font-size-lg);
  }

  .floating-element {
    width: 50px;
    height: 50px;
    font-size: var(--widdx-font-size-lg);
  }

  .status-cards {
    grid-template-columns: 1fr;
    gap: var(--widdx-spacing-3);
  }

  .chart-bars {
    height: 60px;
  }

  .quick-actions {
    flex-direction: column;
  }

  .action-btn {
    flex-direction: row;
    justify-content: center;
    padding: var(--widdx-spacing-2) var(--widdx-spacing-4);
  }
}

/* Responsive Design - Mobile */
@media (max-width: 768px) {
  .hero-section {
    min-height: 100vh;
    padding: var(--widdx-spacing-6) 0;
  }

  .hero-content {
    text-align: center;
    margin-bottom: var(--widdx-spacing-8);
  }

  .hero-badge {
    font-size: var(--widdx-font-size-xs);
    padding: var(--widdx-spacing-2) var(--widdx-spacing-3);
    margin-bottom: var(--widdx-spacing-4);
  }

  .hero-title {
    font-size: var(--widdx-font-size-3xl);
    line-height: 1.1;
    margin-bottom: var(--widdx-spacing-4);
  }

  .hero-subtitle {
    font-size: var(--widdx-font-size-base);
    margin-bottom: var(--widdx-spacing-6);
  }

  .hero-cta {
    flex-direction: column;
    gap: var(--widdx-spacing-3);
    margin-bottom: var(--widdx-spacing-6);
  }

  .hero-cta .btn {
    width: 100%;
    padding: var(--widdx-spacing-3) var(--widdx-spacing-6);
    font-size: var(--widdx-font-size-base);
  }

  .hero-stats .row {
    margin: 0;
  }

  .hero-stats .col-md-3 {
    padding: 0 var(--widdx-spacing-2);
    margin-bottom: var(--widdx-spacing-4);
  }

  .hero-stat-number {
    font-size: var(--widdx-font-size-2xl);
  }

  .hero-stat-label {
    font-size: var(--widdx-font-size-xs);
  }

  .hero-trust {
    margin-top: var(--widdx-spacing-4);
  }

  .trust-logos {
    gap: var(--widdx-spacing-3);
  }

  .trust-logo {
    width: 36px;
    height: 36px;
    font-size: var(--widdx-font-size-base);
  }

  .hero-visual {
    padding: var(--widdx-spacing-4);
    order: -1;
  }

  .dashboard-preview {
    transform: none;
    margin-bottom: var(--widdx-spacing-6);
  }

  .dashboard-content {
    padding: var(--widdx-spacing-4);
  }

  .status-cards {
    grid-template-columns: 1fr;
    gap: var(--widdx-spacing-2);
    margin-bottom: var(--widdx-spacing-4);
  }

  .status-card {
    padding: var(--widdx-spacing-3);
  }

  .status-icon {
    width: 32px;
    height: 32px;
    font-size: var(--widdx-font-size-base);
  }

  .chart-bars {
    height: 50px;
  }

  .quick-actions {
    flex-direction: column;
    gap: var(--widdx-spacing-2);
  }

  .action-btn {
    flex-direction: row;
    justify-content: center;
    padding: var(--widdx-spacing-2);
    font-size: var(--widdx-font-size-xs);
  }

  .floating-elements {
    display: none;
  }

  .scroll-indicator {
    bottom: var(--widdx-spacing-4);
  }

  .scroll-arrow {
    width: 32px;
    height: 32px;
    font-size: var(--widdx-font-size-base);
  }

  .scroll-text {
    font-size: var(--widdx-font-size-xs);
  }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
  .hero-title {
    font-size: var(--widdx-font-size-2xl);
  }

  .hero-subtitle {
    font-size: var(--widdx-font-size-sm);
  }

  .hero-stats .col-md-3 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .dashboard-header {
    padding: var(--widdx-spacing-3);
  }

  .dashboard-title {
    font-size: var(--widdx-font-size-xs);
  }

  .nav-dot {
    width: 8px;
    height: 8px;
  }
}

/* Floating Background Elements */
.hero-bg-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.floating-element-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-element-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.floating-element-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Service Preview Cards */
.services-preview-section {
  background: linear-gradient(135deg, var(--widdx-gray-50), var(--widdx-gray-100));
}

.service-preview-card {
  background: var(--widdx-white);
  padding: var(--widdx-spacing-8);
  border-radius: var(--widdx-radius-lg);
  text-align: center;
  border: 1px solid var(--widdx-gray-200);
  transition: all var(--widdx-transition-normal);
  height: 100%;
  position: relative;
  overflow: hidden;
}

.service-preview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--widdx-primary), var(--widdx-accent));
  transform: scaleX(0);
  transition: transform var(--widdx-transition-normal);
}

.service-preview-card:hover::before {
  transform: scaleX(1);
}

.service-preview-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--widdx-shadow-xl);
  border-color: var(--widdx-primary);
}

.service-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--widdx-spacing-6);
  background: linear-gradient(135deg, var(--widdx-primary), var(--widdx-accent));
  border-radius: var(--widdx-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--widdx-font-size-2xl);
  color: var(--widdx-white);
  transition: all var(--widdx-transition-normal);
}

.service-preview-card:hover .service-icon {
  transform: scale(1.1) rotate(5deg);
}

.service-preview-card h4 {
  font-size: var(--widdx-font-size-xl);
  font-weight: 600;
  margin-bottom: var(--widdx-spacing-4);
  color: var(--widdx-gray-900);
}

.service-preview-card p {
  color: var(--widdx-gray-600);
  margin-bottom: var(--widdx-spacing-6);
  line-height: 1.6;
}

/* Testimonials Section */
.testimonials-section {
  background: var(--widdx-white);
}

.testimonial-card {
  background: var(--widdx-white);
  padding: var(--widdx-spacing-6);
  border-radius: var(--widdx-radius-lg);
  border: 1px solid var(--widdx-gray-200);
  transition: all var(--widdx-transition-normal);
  height: 100%;
  position: relative;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--widdx-shadow-lg);
  border-color: var(--widdx-primary);
}

.testimonial-content {
  margin-bottom: var(--widdx-spacing-6);
}

.testimonial-stars {
  color: var(--widdx-warning);
  font-size: var(--widdx-font-size-sm);
}

.testimonial-text {
  font-style: italic;
  margin-bottom: 0;
  color: var(--widdx-gray-600);
  line-height: 1.7;
  font-size: var(--widdx-font-size-base);
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--widdx-spacing-3);
}

.author-avatar {
  font-size: var(--widdx-font-size-3xl);
  color: var(--widdx-primary);
  opacity: 0.7;
}

.author-name {
  font-weight: 600;
  color: var(--widdx-gray-900);
  font-size: var(--widdx-font-size-base);
  margin-bottom: var(--widdx-spacing-1);
}

.author-company {
  font-size: var(--widdx-font-size-sm);
  color: var(--widdx-gray-500);
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, var(--widdx-primary), var(--widdx-accent));
  color: var(--widdx-white);
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="cta-grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23cta-grid)"/></svg>');
  opacity: 0.3;
}

.cta-content {
  position: relative;
  z-index: 2;
}

.cta-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  margin-bottom: var(--widdx-spacing-4);
  color: var(--widdx-white);
}

.cta-subtitle {
  font-size: var(--widdx-font-size-xl);
  margin-bottom: var(--widdx-spacing-8);
  opacity: 0.9;
  color: var(--widdx-white);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  gap: var(--widdx-spacing-4);
  justify-content: center;
  flex-wrap: wrap;
}

.cta-buttons .btn {
  padding: var(--widdx-spacing-4) var(--widdx-spacing-8);
  font-size: var(--widdx-font-size-lg);
  font-weight: 600;
  border-radius: var(--widdx-radius-lg);
}

.cta-buttons .btn-primary {
  background: var(--widdx-white);
  color: var(--widdx-primary);
  border: 2px solid var(--widdx-white);
}

.cta-buttons .btn-primary:hover {
  background: transparent;
  color: var(--widdx-white);
  border-color: var(--widdx-white);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.cta-buttons .btn-outline-primary {
  background: transparent;
  color: var(--widdx-white);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.cta-buttons .btn-outline-primary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--widdx-white);
  color: var(--widdx-white);
  transform: translateY(-2px);
}

/* Text Gradient Utility */
.text-gradient {
  background: linear-gradient(135deg, var(--widdx-accent), var(--widdx-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: var(--widdx-primary); /* Fallback for browsers that don't support background-clip */
}

/* Pricing Page Specific Styles */
.pricing-hero-section {
  background: linear-gradient(135deg, var(--widdx-gray-50), var(--widdx-gray-100));
}

.billing-toggle .btn-group {
  box-shadow: var(--widdx-shadow-sm);
  border-radius: var(--widdx-radius-lg);
  overflow: hidden;
}

.billing-toggle .btn {
  padding: var(--widdx-spacing-3) var(--widdx-spacing-6);
  font-weight: 500;
  border: none;
  background: var(--widdx-white);
  color: var(--widdx-gray-600);
  transition: all var(--widdx-transition-fast);
}

.billing-toggle .btn:hover {
  background: var(--widdx-gray-100);
  color: var(--widdx-gray-800);
}

.billing-toggle .btn.active,
.billing-toggle .btn:checked {
  background: var(--widdx-primary);
  color: var(--widdx-white);
  box-shadow: var(--widdx-shadow-md);
}

.pricing-header {
  text-align: center;
  margin-bottom: var(--widdx-spacing-6);
}

.pricing-description {
  color: var(--widdx-gray-600);
  font-size: var(--widdx-font-size-base);
  margin-bottom: 0;
}

.pricing-price-section {
  text-align: center;
  margin-bottom: var(--widdx-spacing-8);
}

.pricing-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: var(--widdx-spacing-1);
}

.pricing-price .currency {
  font-size: var(--widdx-font-size-xl);
  font-weight: 600;
  color: var(--widdx-gray-600);
}

.pricing-price .amount {
  font-size: var(--widdx-font-size-5xl);
  font-weight: 700;
  color: var(--widdx-primary);
}

.pricing-price .period {
  font-size: var(--widdx-font-size-lg);
  color: var(--widdx-gray-500);
}

.pricing-features {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--widdx-spacing-8) 0;
}

.pricing-features li {
  display: flex;
  align-items: center;
  gap: var(--widdx-spacing-3);
  padding: var(--widdx-spacing-3) 0;
  color: var(--widdx-gray-700);
  border-bottom: 1px solid var(--widdx-gray-100);
}

.pricing-features li:last-child {
  border-bottom: none;
}

.pricing-features li i {
  color: var(--widdx-success);
  font-size: var(--widdx-font-size-sm);
  width: 16px;
  text-align: center;
}

.pricing-action {
  margin-top: auto;
}

/* Features Comparison Table */
.features-comparison-section {
  background: linear-gradient(135deg, var(--widdx-gray-50), var(--widdx-gray-100));
}

.comparison-table-wrapper {
  background: var(--widdx-white);
  border-radius: var(--widdx-radius-lg);
  overflow: hidden;
  box-shadow: var(--widdx-shadow-md);
}

.comparison-table {
  margin: 0;
  border: none;
}

.comparison-table th {
  background: var(--widdx-gray-50);
  border: none;
  padding: var(--widdx-spacing-4) var(--widdx-spacing-3);
  font-weight: 600;
  color: var(--widdx-gray-800);
  font-size: var(--widdx-font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.comparison-table td {
  border: none;
  padding: var(--widdx-spacing-4) var(--widdx-spacing-3);
  border-bottom: 1px solid var(--widdx-gray-100);
  color: var(--widdx-gray-700);
}

.comparison-table .featured-column {
  background: linear-gradient(135deg, rgba(30, 64, 175, 0.05), rgba(124, 58, 237, 0.05));
  position: relative;
}

.comparison-table .featured-column::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--widdx-primary), var(--widdx-accent));
}

.comparison-table tbody tr:last-child td {
  border-bottom: none;
}

/* FAQ Section */
.faq-section {
  background: var(--widdx-white);
}

.accordion-item {
  border: 1px solid var(--widdx-gray-200);
  border-radius: var(--widdx-radius-lg);
  margin-bottom: var(--widdx-spacing-4);
  overflow: hidden;
}

.accordion-button {
  background: var(--widdx-white);
  border: none;
  padding: var(--widdx-spacing-4) var(--widdx-spacing-6);
  font-weight: 600;
  color: var(--widdx-gray-800);
  font-size: var(--widdx-font-size-base);
  transition: all var(--widdx-transition-fast);
}

.accordion-button:not(.collapsed) {
  background: linear-gradient(135deg, var(--widdx-primary), var(--widdx-accent));
  color: var(--widdx-white);
  box-shadow: none;
}

.accordion-button:focus {
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
  border: none;
}

.accordion-button::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23374151'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  transition: transform var(--widdx-transition-fast);
}

.accordion-button:not(.collapsed)::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  transform: rotate(180deg);
}

.accordion-body {
  padding: var(--widdx-spacing-6);
  background: var(--widdx-gray-50);
  color: var(--widdx-gray-700);
  line-height: 1.7;
}

/* Responsive Design for Pricing */
@media (max-width: 768px) {
  .pricing-grid {
    grid-template-columns: 1fr;
    gap: var(--widdx-spacing-6);
  }

  .pricing-card.featured {
    transform: none;
    order: -1;
  }

  .billing-toggle {
    margin-bottom: var(--widdx-spacing-8);
  }

  .billing-toggle .btn {
    padding: var(--widdx-spacing-2) var(--widdx-spacing-4);
    font-size: var(--widdx-font-size-sm);
  }

  .pricing-price .amount {
    font-size: var(--widdx-font-size-4xl);
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-buttons .btn {
    width: 100%;
    max-width: 300px;
  }
}

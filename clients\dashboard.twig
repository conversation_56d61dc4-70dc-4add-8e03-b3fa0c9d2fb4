{% extends "base.twig" %}

{% block meta_title %}{{ 'Dashboard'|trans }}{% endblock %}

{% block content %}
    <!-- Welcome Section -->
    <div class="welcome-section fade-in-up">
        <div class="welcome-content">
            <h1 class="welcome-title">
                {{ 'Welcome back'|trans }}, {{ client.first_name }}!
            </h1>
            <p class="welcome-subtitle">
                {{ 'Here\'s an overview of your account and services'|trans }}
            </p>
            
            <div class="welcome-stats">
                <div class="welcome-stat">
                    <span class="welcome-stat-value">{{ stats.active_services|default(0) }}</span>
                    <span class="welcome-stat-label">{{ 'Active Services'|trans }}</span>
                </div>
                <div class="welcome-stat">
                    <span class="welcome-stat-value">{{ stats.pending_invoices|default(0) }}</span>
                    <span class="welcome-stat-label">{{ 'Pending Invoices'|trans }}</span>
                </div>
                <div class="welcome-stat">
                    <span class="welcome-stat-value">{{ stats.support_tickets|default(0) }}</span>
                    <span class="welcome-stat-label">{{ 'Open Tickets'|trans }}</span>
                </div>
                <div class="welcome-stat">
                    <span class="welcome-stat-value">{{ profile.balance | money(profile.currency) }}</span>
                    <span class="welcome-stat-label">{{ 'Account Balance'|trans }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Cards -->
    <div class="dashboard-cards">
        <!-- Quick Actions Card -->
        <div class="dashboard-card fade-in-left">
            <div class="dashboard-card-header">
                <h3 class="dashboard-card-title">{{ 'Quick Actions'|trans }}</h3>
                <div class="dashboard-card-icon">
                    <i class="fas fa-bolt"></i>
                </div>
            </div>
            <div class="dashboard-card-body">
                <div class="quick-actions-grid">
                    <a href="{{ '/order'|link }}" class="quick-action-item">
                        <div class="quick-action-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <span>{{ 'Order Service'|trans }}</span>
                    </a>
                    <a href="{{ '/invoice'|link }}" class="quick-action-item">
                        <div class="quick-action-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <span>{{ 'View Invoices'|trans }}</span>
                    </a>
                    <a href="{{ '/support'|link }}" class="quick-action-item">
                        <div class="quick-action-icon">
                            <i class="fas fa-life-ring"></i>
                        </div>
                        <span>{{ 'Get Support'|trans }}</span>
                    </a>
                    <a href="{{ '/client/profile'|link }}" class="quick-action-item">
                        <div class="quick-action-icon">
                            <i class="fas fa-user-cog"></i>
                        </div>
                        <span>{{ 'Edit Profile'|trans }}</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Account Overview Card -->
        <div class="dashboard-card fade-in-up">
            <div class="dashboard-card-header">
                <h3 class="dashboard-card-title">{{ 'Account Overview'|trans }}</h3>
                <div class="dashboard-card-icon">
                    <i class="fas fa-chart-pie"></i>
                </div>
            </div>
            <div class="dashboard-card-body">
                <div class="account-overview-stats">
                    <div class="overview-stat">
                        <div class="overview-stat-label">{{ 'Member Since'|trans }}</div>
                        <div class="overview-stat-value">{{ client.created_at|date('M Y') }}</div>
                    </div>
                    <div class="overview-stat">
                        <div class="overview-stat-label">{{ 'Total Orders'|trans }}</div>
                        <div class="overview-stat-value">{{ stats.total_orders|default(0) }}</div>
                    </div>
                    <div class="overview-stat">
                        <div class="overview-stat-label">{{ 'Account Status'|trans }}</div>
                        <div class="overview-stat-value">
                            <span class="status-badge {{ client.status == 'active' ? 'active' : 'suspended' }}">
                                {{ client.status|title }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity Card -->
        <div class="dashboard-card fade-in-right">
            <div class="dashboard-card-header">
                <h3 class="dashboard-card-title">{{ 'Recent Activity'|trans }}</h3>
                <div class="dashboard-card-icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
            <div class="dashboard-card-body">
                <div class="activity-list">
                    {% if recent_activities is defined and recent_activities %}
                        {% for activity in recent_activities|slice(0, 5) %}
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-{{ activity.icon|default('circle') }}"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">{{ activity.title }}</div>
                                    <div class="activity-time">{{ activity.created_at|date('M j, Y g:i A') }}</div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-history fa-2x text-muted mb-3"></i>
                            <p class="text-muted">{{ 'No recent activity'|trans }}</p>
                        </div>
                    {% endif %}
                </div>
                {% if recent_activities and recent_activities|length > 5 %}
                    <div class="card-footer">
                        <a href="{{ '/client/activity'|link }}" class="btn btn-outline-primary btn-sm">
                            {{ 'View All Activity'|trans }}
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Services and Invoices Row -->
    <div class="row">
        <!-- Active Services -->
        <div class="col-lg-6 mb-4">
            <div class="card fade-in-left">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">{{ 'Active Services'|trans }}</h4>
                    <a href="{{ '/order/service'|link }}" class="btn btn-outline-primary btn-sm">
                        {{ 'View All'|trans }}
                    </a>
                </div>
                <div class="card-body">
                    {% if services is defined and services %}
                        <div class="services-list">
                            {% for service in services|slice(0, 3) %}
                                <div class="service-item">
                                    <div class="service-info">
                                        <div class="service-name">{{ service.title }}</div>
                                        <div class="service-details">
                                            <span class="service-price">{{ service.total | money(service.currency) }}</span>
                                            <span class="service-period">/{{ service.period|trans }}</span>
                                        </div>
                                    </div>
                                    <div class="service-status">
                                        <span class="status-badge {{ service.status }}">
                                            {{ service.status|title }}
                                        </span>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state text-center py-4">
                            <i class="fas fa-server fa-2x text-muted mb-3"></i>
                            <p class="text-muted mb-3">{{ 'No active services'|trans }}</p>
                            <a href="{{ '/order'|link }}" class="btn btn-primary">
                                {{ 'Order Your First Service'|trans }}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Invoices -->
        <div class="col-lg-6 mb-4">
            <div class="card fade-in-right">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">{{ 'Recent Invoices'|trans }}</h4>
                    <a href="{{ '/invoice'|link }}" class="btn btn-outline-primary btn-sm">
                        {{ 'View All'|trans }}
                    </a>
                </div>
                <div class="card-body">
                    {% if invoices is defined and invoices %}
                        <div class="invoices-list">
                            {% for invoice in invoices|slice(0, 3) %}
                                <div class="invoice-item">
                                    <div class="invoice-info">
                                        <div class="invoice-number">#{{ invoice.serie_nr }}</div>
                                        <div class="invoice-date">{{ invoice.created_at|date('M j, Y') }}</div>
                                    </div>
                                    <div class="invoice-amount">
                                        <span class="amount">{{ invoice.total | money(invoice.currency) }}</span>
                                        <span class="status-badge {{ invoice.status }}">
                                            {{ invoice.status|title }}
                                        </span>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state text-center py-4">
                            <i class="fas fa-file-invoice fa-2x text-muted mb-3"></i>
                            <p class="text-muted">{{ 'No invoices yet'|trans }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Support Tickets (if support module is enabled) -->
    {% if guest.extension_is_on({"mod":"support"}) %}
        <div class="row">
            <div class="col-12">
                <div class="card fade-in-up">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">{{ 'Recent Support Tickets'|trans }}</h4>
                        <div>
                            <a href="{{ '/support'|link }}" class="btn btn-outline-primary btn-sm me-2">
                                {{ 'View All'|trans }}
                            </a>
                            <a href="{{ '/support/ticket/new'|link }}" class="btn btn-primary btn-sm">
                                {{ 'New Ticket'|trans }}
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if tickets is defined and tickets %}
                            <div class="data-table">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>{{ 'Subject'|trans }}</th>
                                            <th>{{ 'Status'|trans }}</th>
                                            <th>{{ 'Priority'|trans }}</th>
                                            <th>{{ 'Last Update'|trans }}</th>
                                            <th>{{ 'Actions'|trans }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for ticket in tickets|slice(0, 5) %}
                                            <tr>
                                                <td>
                                                    <a href="{{ '/support/ticket'|link }}/{{ ticket.id }}" class="text-decoration-none">
                                                        {{ ticket.subject }}
                                                    </a>
                                                </td>
                                                <td>
                                                    <span class="status-badge {{ ticket.status }}">
                                                        {{ ticket.status|title }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="priority-badge priority-{{ ticket.priority }}">
                                                        {{ ticket.priority|title }}
                                                    </span>
                                                </td>
                                                <td>{{ ticket.updated_at|date('M j, Y') }}</td>
                                                <td>
                                                    <a href="{{ '/support/ticket'|link }}/{{ ticket.id }}" class="btn btn-outline-primary btn-sm">
                                                        {{ 'View'|trans }}
                                                    </a>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="empty-state text-center py-4">
                                <i class="fas fa-life-ring fa-2x text-muted mb-3"></i>
                                <p class="text-muted mb-3">{{ 'No support tickets'|trans }}</p>
                                <a href="{{ '/support/ticket/new'|link }}" class="btn btn-primary">
                                    {{ 'Create Your First Ticket'|trans }}
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
{% endblock %}

{% block css %}
<style>
    /* Quick Actions Grid */
    .quick-actions-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--widdx-spacing-4);
    }
    
    .quick-action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: var(--widdx-spacing-4);
        border: 1px solid var(--widdx-gray-200);
        border-radius: var(--widdx-radius-md);
        text-decoration: none;
        color: var(--widdx-gray-700);
        transition: all var(--widdx-transition-fast);
        text-align: center;
    }
    
    .quick-action-item:hover {
        border-color: var(--widdx-primary);
        color: var(--widdx-primary);
        transform: translateY(-2px);
        box-shadow: var(--widdx-shadow-md);
        text-decoration: none;
    }
    
    .quick-action-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, var(--widdx-primary), var(--widdx-accent));
        border-radius: var(--widdx-radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--widdx-white);
        font-size: var(--widdx-font-size-lg);
        margin-bottom: var(--widdx-spacing-2);
        transition: all var(--widdx-transition-fast);
    }
    
    .quick-action-item:hover .quick-action-icon {
        transform: scale(1.1);
    }
    
    /* Account Overview Stats */
    .account-overview-stats {
        display: grid;
        grid-template-columns: 1fr;
        gap: var(--widdx-spacing-4);
    }
    
    .overview-stat {
        text-align: center;
        padding: var(--widdx-spacing-3);
        border: 1px solid var(--widdx-gray-200);
        border-radius: var(--widdx-radius-md);
    }
    
    .overview-stat-label {
        font-size: var(--widdx-font-size-sm);
        color: var(--widdx-gray-500);
        margin-bottom: var(--widdx-spacing-1);
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .overview-stat-value {
        font-size: var(--widdx-font-size-lg);
        font-weight: 600;
        color: var(--widdx-gray-900);
    }
    
    /* Activity List */
    .activity-list {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .activity-item {
        display: flex;
        align-items: center;
        gap: var(--widdx-spacing-3);
        padding: var(--widdx-spacing-3) 0;
        border-bottom: 1px solid var(--widdx-gray-100);
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-icon {
        width: 32px;
        height: 32px;
        background: var(--widdx-gray-100);
        border-radius: var(--widdx-radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--widdx-primary);
        font-size: var(--widdx-font-size-sm);
    }
    
    .activity-title {
        font-weight: 500;
        color: var(--widdx-gray-800);
        font-size: var(--widdx-font-size-sm);
    }
    
    .activity-time {
        font-size: var(--widdx-font-size-xs);
        color: var(--widdx-gray-500);
    }
    
    /* Services and Invoices Lists */
    .service-item,
    .invoice-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--widdx-spacing-3) 0;
        border-bottom: 1px solid var(--widdx-gray-100);
    }
    
    .service-item:last-child,
    .invoice-item:last-child {
        border-bottom: none;
    }
    
    .service-name,
    .invoice-number {
        font-weight: 500;
        color: var(--widdx-gray-800);
        margin-bottom: var(--widdx-spacing-1);
    }
    
    .service-details,
    .invoice-date {
        font-size: var(--widdx-font-size-sm);
        color: var(--widdx-gray-500);
    }
    
    .service-price,
    .amount {
        font-weight: 600;
        color: var(--widdx-primary);
    }
    
    /* Priority Badges */
    .priority-badge {
        display: inline-flex;
        align-items: center;
        padding: var(--widdx-spacing-1) var(--widdx-spacing-2);
        border-radius: var(--widdx-radius-sm);
        font-size: var(--widdx-font-size-xs);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .priority-low {
        background: rgba(16, 185, 129, 0.1);
        color: var(--widdx-success);
    }
    
    .priority-medium {
        background: rgba(245, 158, 11, 0.1);
        color: var(--widdx-warning);
    }
    
    .priority-high {
        background: rgba(239, 68, 68, 0.1);
        color: var(--widdx-error);
    }
    
    /* Empty States */
    .empty-state {
        text-align: center;
        padding: var(--widdx-spacing-8);
    }
    
    .empty-state i {
        opacity: 0.5;
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .quick-actions-grid {
            grid-template-columns: 1fr;
        }
        
        .dashboard-cards {
            grid-template-columns: 1fr;
        }
        
        .welcome-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{% endblock %}

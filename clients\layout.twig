<!-- WIDDX Clients Layout - Authenticated User Dashboard -->
<div class="dashboard-layout">
    <!-- Dashboard Header -->
    <header class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center py-3">
                <!-- Brand/Logo -->
                <div class="col-auto">
                    <a class="navbar-brand d-flex align-items-center" href="{{ '/'|link }}">
                        {% if guest.system_company.logo_url %}
                            <img src="{{ guest.system_company.logo_url }}" alt="{{ guest.system_company.name }}" class="navbar-logo me-2" style="height: 32px;">
                        {% endif %}
                        <span class="fw-bold">{{ guest.system_company.name }}</span>
                    </a>
                </div>
                
                <!-- Top Navigation -->
                <div class="col">
                    <nav class="navbar-nav d-none d-lg-flex flex-row ms-auto">
                        {% if settings.top_menu_dashboard %}
                            <a class="nav-link px-3" href="{{ '/'|link }}">
                                <i class="fas fa-tachometer-alt me-1"></i>
                                {{ 'Dashboard'|trans }}
                            </a>
                        {% endif %}
                        
                        {% if settings.top_menu_order %}
                            <a class="nav-link px-3" href="{{ '/order'|link }}">
                                <i class="fas fa-shopping-cart me-1"></i>
                                {{ 'Order'|trans }}
                            </a>
                        {% endif %}
                        
                        {% if settings.top_menu_profile %}
                            <a class="nav-link px-3" href="{{ '/client/profile'|link }}">
                                <i class="fas fa-user me-1"></i>
                                {{ 'Profile'|trans }}
                            </a>
                        {% endif %}
                    </nav>
                </div>
                
                <!-- User Menu -->
                <div class="col-auto">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                            <div class="user-avatar me-2">
                                <i class="fas fa-user-circle fa-lg"></i>
                            </div>
                            <div class="user-info d-none d-md-block text-start">
                                <div class="user-name fw-semibold">{{ client.first_name }} {{ client.last_name }}</div>
                                <div class="user-email small text-muted">{{ client.email }}</div>
                            </div>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <h6 class="dropdown-header">
                                    {{ client.first_name }} {{ client.last_name }}
                                    <br><small class="text-muted">{{ client.email }}</small>
                                </h6>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ '/client/profile'|link }}">
                                    <i class="fas fa-user me-2"></i>
                                    {{ 'Profile'|trans }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ '/client/security'|link }}">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    {{ 'Security'|trans }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ '/client/api'|link }}">
                                    <i class="fas fa-code me-2"></i>
                                    {{ 'API'|trans }}
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            {% if settings.top_menu_signout %}
                                <li>
                                    <a class="dropdown-item text-danger" href="{{ '/client/logout'|link }}">
                                        <i class="fas fa-sign-out-alt me-2"></i>
                                        {{ 'Logout'|trans }}
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
                
                <!-- Mobile Menu Toggle -->
                <div class="col-auto d-lg-none">
                    <button class="btn btn-outline-secondary sidebar-toggle" type="button">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Dashboard Main Content -->
    <div class="dashboard-main">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar">
            <!-- Account Balance Widget -->
            {% if settings.sidebar_balance_enabled and client %}
                <div class="balance-widget mb-4">
                    <div class="balance-widget-content">
                        <div class="balance-widget-label">{{ 'Account Balance'|trans }}</div>
                        <div class="balance-widget-amount">{{ profile.balance | money(profile.currency) }}</div>
                    </div>
                </div>
            {% endif %}

            <!-- Sidebar Navigation -->
            <nav class="sidebar-navigation">
                <ul class="sidebar-nav">
                    {% if settings.side_menu_dashboard %}
                        <li class="sidebar-nav-item">
                            <a class="sidebar-nav-link" href="{{ '/'|link }}">
                                <i class="sidebar-nav-icon fas fa-tachometer-alt"></i>
                                {{ 'Dashboard'|trans }}
                            </a>
                        </li>
                    {% endif %}

                    {% if settings.side_menu_order %}
                        <li class="sidebar-nav-item">
                            <a class="sidebar-nav-link" href="{{ '/order'|link }}">
                                <i class="sidebar-nav-icon fas fa-shopping-cart"></i>
                                {{ 'Order'|trans }}
                            </a>
                        </li>
                    {% endif %}

                    {% if settings.side_menu_services %}
                        <li class="sidebar-nav-item">
                            <a class="sidebar-nav-link" href="{{ '/order/service'|link }}">
                                <i class="sidebar-nav-icon fas fa-server"></i>
                                {{ 'Services'|trans }}
                            </a>
                        </li>
                    {% endif %}

                    {% if settings.side_menu_invoices %}
                        <li class="sidebar-nav-item">
                            <a class="sidebar-nav-link" href="{{ '/invoice'|link }}">
                                <i class="sidebar-nav-icon fas fa-file-invoice"></i>
                                {{ 'Invoices'|trans }}
                            </a>
                        </li>
                    {% endif %}

                    {% if settings.side_menu_payments %}
                        <li class="sidebar-nav-item">
                            <a class="sidebar-nav-link" href="{{ '/client/balance'|link }}">
                                <i class="sidebar-nav-icon fas fa-wallet"></i>
                                {{ 'Wallet'|trans }}
                            </a>
                        </li>
                    {% endif %}

                    {% if settings.side_menu_support %}
                        <li class="sidebar-nav-item">
                            <a class="sidebar-nav-link" href="{{ '/support'|link }}">
                                <i class="sidebar-nav-icon fas fa-life-ring"></i>
                                {{ 'Support'|trans }}
                            </a>
                        </li>
                    {% endif %}

                    {% if settings.side_menu_emails %}
                        <li class="sidebar-nav-item">
                            <a class="sidebar-nav-link" href="{{ '/email'|link }}">
                                <i class="sidebar-nav-icon fas fa-envelope"></i>
                                {{ 'Email'|trans }}
                            </a>
                        </li>
                    {% endif %}

                    {% if (guest.support_kb_enabled() and settings.side_menu_kb) %}
                        <li class="sidebar-nav-item">
                            <a class="sidebar-nav-link" href="{{ 'support/kb'|link }}">
                                <i class="sidebar-nav-icon fas fa-book"></i>
                                {{ 'Knowledge Base'|trans }}
                            </a>
                        </li>
                    {% endif %}

                    {% if (guest.extension_is_on({"mod":"news"}) and settings.side_menu_news) %}
                        <li class="sidebar-nav-item">
                            <a class="sidebar-nav-link" href="{{ '/news'|link }}">
                                <i class="sidebar-nav-icon fas fa-newspaper"></i>
                                {{ 'News'|trans }}
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>

            <!-- Sidebar Note Widget -->
            {% if settings.sidebar_note_enabled %}
                <div class="sidebar-note mt-4 p-3 bg-info bg-opacity-10 rounded">
                    <h6 class="text-info mb-2">{{ settings.sidebar_note_title }}</h6>
                    <small class="text-muted">{{ settings.sidebar_note_content }}</small>
                </div>
            {% endif %}
        </aside>

        <!-- Main Content Area -->
        <main class="dashboard-content">
            <!-- Breadcrumb -->
            {% if settings.show_breadcrumb and not settings.hide_dashboard_breadcrumb %}
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{{ '/'|link }}">
                                <i class="fas fa-home me-1"></i>
                                {{ 'Dashboard'|trans }}
                            </a>
                        </li>
                        {% block breadcrumb %}{% endblock %}
                    </ol>
                </nav>
            {% endif %}

            <!-- Flash Messages -->
            {% for message in flashes %}
                <div class="alert alert-{{ message.type }} alert-dismissible fade show" role="alert">
                    <i class="fas fa-{{ message.type == 'error' ? 'exclamation-triangle' : (message.type == 'success' ? 'check-circle' : 'info-circle') }} me-2"></i>
                    {{ message.message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}

            <!-- Showcase Section -->
            {% if settings.showcase_enabled %}
                <div class="showcase-section mb-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="showcase-content">
                                        {{ settings.showcase_text|markdown }}
                                    </div>
                                </div>
                                {% if settings.showcase_button_title and settings.showcase_button_url %}
                                    <div class="col-md-4 text-md-end">
                                        <a href="{{ settings.showcase_button_url }}" class="btn btn-primary btn-lg">
                                            {{ settings.showcase_button_title }}
                                        </a>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Page Content -->
            {% block content %}
                <!-- Default dashboard content will be replaced by specific page templates -->
            {% endblock %}
        </main>
    </div>
</div>

<!-- Mobile Sidebar Overlay -->
<div class="sidebar-overlay d-lg-none"></div>

<!-- Dashboard JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mobile sidebar toggle
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        const sidebar = document.querySelector('.dashboard-sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        
        if (sidebarToggle && sidebar && overlay) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('mobile-open');
                overlay.classList.toggle('show');
                document.body.classList.toggle('sidebar-open');
            });
            
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('mobile-open');
                overlay.classList.remove('show');
                document.body.classList.remove('sidebar-open');
            });
        }
        
        // Auto-hide alerts
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(alert => {
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        alert.remove();
                    }, 300);
                }
            }, 5000);
        });
    });
</script>

<!-- Additional Mobile Styles -->
<style>
    @media (max-width: 991.98px) {
        .dashboard-sidebar {
            position: fixed;
            top: 0;
            left: -280px;
            height: 100vh;
            z-index: 1050;
            transition: left 0.3s ease;
        }
        
        .dashboard-sidebar.mobile-open {
            left: 0;
        }
        
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1040;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        
        .dashboard-content {
            padding: var(--widdx-spacing-4);
        }
        
        body.sidebar-open {
            overflow: hidden;
        }
    }
</style>

/**
 * WIDDX Clients JavaScript
 * Dashboard functionality, client-specific interactions
 */

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeClientFeatures();
});

/**
 * Initialize all client dashboard features
 */
function initializeClientFeatures() {
    initializeDashboardAnimations();
    initializeSidebarNavigation();
    initializeDataTables();
    initializeServiceCards();
    initializeInvoiceCards();
    initializeBalanceWidget();
    initializeNotifications();
    initializeQuickActions();
}

/**
 * Initialize dashboard animations
 */
function initializeDashboardAnimations() {
    // Animate dashboard cards on load
    const dashboardCards = document.querySelectorAll('.dashboard-card');
    dashboardCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Animate welcome section
    const welcomeSection = document.querySelector('.welcome-section');
    if (welcomeSection) {
        welcomeSection.style.opacity = '0';
        welcomeSection.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            welcomeSection.style.transition = 'all 0.8s ease-out';
            welcomeSection.style.opacity = '1';
            welcomeSection.style.transform = 'translateY(0)';
        }, 200);
    }

    // Animate sidebar navigation
    const sidebarItems = document.querySelectorAll('.sidebar-nav-item');
    sidebarItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-20px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.4s ease-out';
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
        }, index * 50);
    });
}

/**
 * Initialize sidebar navigation interactions
 */
function initializeSidebarNavigation() {
    const navLinks = document.querySelectorAll('.sidebar-nav-link');
    const currentPath = window.location.pathname;

    // Set active navigation item
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.includes(href.replace(/^\//, ''))) {
            link.classList.add('active');
        }

        // Add hover effects
        link.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateX(8px)';
            }
        });

        link.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateX(0)';
            }
        });
    });

    // Mobile sidebar toggle
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.dashboard-sidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('mobile-open');
            document.body.classList.toggle('sidebar-open');
        });
    }
}

/**
 * Initialize data table interactions
 */
function initializeDataTables() {
    const tables = document.querySelectorAll('.data-table table');
    
    tables.forEach(table => {
        // Add row hover effects
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.01)';
                this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
            });

            row.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = 'none';
            });
        });

        // Add sortable column headers
        const headers = table.querySelectorAll('th[data-sortable]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                sortTable(table, this);
            });
        });
    });
}

/**
 * Sort table by column
 */
function sortTable(table, header) {
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const isAscending = !header.classList.contains('sort-asc');

    // Sort rows
    rows.sort((a, b) => {
        const aText = a.children[columnIndex].textContent.trim();
        const bText = b.children[columnIndex].textContent.trim();
        
        // Try to parse as numbers
        const aNum = parseFloat(aText.replace(/[^0-9.-]/g, ''));
        const bNum = parseFloat(bText.replace(/[^0-9.-]/g, ''));
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        }
        
        // Sort as strings
        return isAscending ? aText.localeCompare(bText) : bText.localeCompare(aText);
    });

    // Update header classes
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');

    // Reorder rows in DOM
    rows.forEach(row => tbody.appendChild(row));
}

/**
 * Initialize service card interactions
 */
function initializeServiceCards() {
    const serviceCards = document.querySelectorAll('.service-card');
    
    serviceCards.forEach(card => {
        // Add expand/collapse functionality
        const header = card.querySelector('.service-card-header');
        const details = card.querySelector('.service-card-details');
        
        if (header && details) {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                card.classList.toggle('expanded');
                
                if (card.classList.contains('expanded')) {
                    details.style.maxHeight = details.scrollHeight + 'px';
                } else {
                    details.style.maxHeight = '0';
                }
            });
        }

        // Add action button interactions
        const actionButtons = card.querySelectorAll('.btn');
        actionButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                
                // Add loading state
                const originalText = this.textContent;
                this.textContent = 'Loading...';
                this.disabled = true;
                
                // Simulate API call
                setTimeout(() => {
                    this.textContent = originalText;
                    this.disabled = false;
                }, 1000);
            });
        });
    });
}

/**
 * Initialize invoice card interactions
 */
function initializeInvoiceCards() {
    const invoiceCards = document.querySelectorAll('.invoice-card');
    
    invoiceCards.forEach(card => {
        card.addEventListener('click', function() {
            // Add selection state
            invoiceCards.forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
            
            // Animate selection
            this.style.transform = 'scale(1.02)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 200);
        });

        // Add payment button functionality
        const payButton = card.querySelector('.btn-pay');
        if (payButton) {
            payButton.addEventListener('click', function(e) {
                e.stopPropagation();
                
                // Show payment modal or redirect
                showPaymentModal(card);
            });
        }
    });
}

/**
 * Show payment modal (placeholder)
 */
function showPaymentModal(invoiceCard) {
    const invoiceNumber = invoiceCard.querySelector('.invoice-number').textContent;
    const amount = invoiceCard.querySelector('.invoice-amount').textContent;
    
    // This would typically open a payment modal
    console.log(`Opening payment for invoice ${invoiceNumber} - ${amount}`);
    
    // For now, just show a simple alert
    alert(`Payment for ${invoiceNumber} - ${amount}\n\nThis would open the payment gateway.`);
}

/**
 * Initialize balance widget interactions
 */
function initializeBalanceWidget() {
    const balanceWidget = document.querySelector('.balance-widget');
    if (!balanceWidget) return;

    // Add click to expand functionality
    balanceWidget.addEventListener('click', function() {
        this.classList.toggle('expanded');
        
        // Show additional balance details
        const details = this.querySelector('.balance-details');
        if (details) {
            details.style.display = this.classList.contains('expanded') ? 'block' : 'none';
        }
    });

    // Animate balance amount on load
    const balanceAmount = balanceWidget.querySelector('.balance-widget-amount');
    if (balanceAmount) {
        const targetAmount = balanceAmount.textContent;
        balanceAmount.textContent = '$0.00';
        
        setTimeout(() => {
            balanceAmount.style.transition = 'all 1s ease-out';
            balanceAmount.textContent = targetAmount;
        }, 500);
    }
}

/**
 * Initialize notification system
 */
function initializeNotifications() {
    // Auto-hide notifications after 5 seconds
    const notifications = document.querySelectorAll('.alert');
    notifications.forEach(notification => {
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    });

    // Add close button functionality
    const closeButtons = document.querySelectorAll('.alert .btn-close');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const alert = this.closest('.alert');
            alert.style.opacity = '0';
            alert.style.transform = 'translateX(100%)';
            setTimeout(() => {
                alert.remove();
            }, 300);
        });
    });
}

/**
 * Initialize quick actions
 */
function initializeQuickActions() {
    const quickActionButtons = document.querySelectorAll('[data-quick-action]');
    
    quickActionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const action = this.getAttribute('data-quick-action');
            const confirm = this.getAttribute('data-confirm');
            
            if (confirm && !window.confirm(confirm)) {
                return;
            }
            
            // Add loading state
            const originalText = this.textContent;
            const originalIcon = this.querySelector('i');
            
            this.disabled = true;
            if (originalIcon) {
                originalIcon.className = 'fas fa-spinner fa-spin';
            }
            this.textContent = 'Processing...';
            
            // Simulate API call
            setTimeout(() => {
                this.disabled = false;
                this.textContent = originalText;
                if (originalIcon) {
                    originalIcon.className = originalIcon.getAttribute('data-original-class') || 'fas fa-check';
                }
                
                // Show success message
                showNotification('Action completed successfully!', 'success');
            }, 1500);
        });
    });
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" aria-label="Close"></button>
    `;
    
    // Insert at the top of the dashboard content
    const dashboardContent = document.querySelector('.dashboard-content');
    if (dashboardContent) {
        dashboardContent.insertBefore(notification, dashboardContent.firstChild);
    }
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
    
    // Add close button functionality
    const closeButton = notification.querySelector('.btn-close');
    closeButton.addEventListener('click', function() {
        notification.style.opacity = '0';
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
}

/**
 * Initialize responsive dashboard
 */
function initializeResponsiveDashboard() {
    const sidebar = document.querySelector('.dashboard-sidebar');
    const content = document.querySelector('.dashboard-content');
    
    if (!sidebar || !content) return;
    
    function handleResize() {
        if (window.innerWidth <= 1024) {
            sidebar.classList.add('mobile');
            content.classList.add('mobile');
        } else {
            sidebar.classList.remove('mobile');
            content.classList.remove('mobile');
        }
    }
    
    window.addEventListener('resize', handleResize);
    handleResize(); // Initial call
}

/**
 * Initialize invoice page functionality
 */
function initializeInvoicePage() {
    // Filter toggle
    const filterBtn = document.getElementById('filterBtn');
    const filterPanel = document.getElementById('filterPanel');

    if (filterBtn && filterPanel) {
        filterBtn.addEventListener('click', function() {
            if (filterPanel.style.display === 'none' || !filterPanel.style.display) {
                filterPanel.style.display = 'block';
                filterBtn.classList.add('active');
                // Animate panel appearance
                filterPanel.style.opacity = '0';
                filterPanel.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    filterPanel.style.transition = 'all 0.3s ease-out';
                    filterPanel.style.opacity = '1';
                    filterPanel.style.transform = 'translateY(0)';
                }, 10);
            } else {
                filterPanel.style.opacity = '0';
                filterPanel.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    filterPanel.style.display = 'none';
                    filterBtn.classList.remove('active');
                }, 300);
            }
        });
    }

    // Export functionality
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            // Show loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Exporting...';
            this.disabled = true;

            // Simulate export process
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
                showNotification('Invoice data exported successfully!', 'success');
            }, 2000);
        });
    }

    // Invoice row interactions
    const invoiceRows = document.querySelectorAll('.invoice-row');
    invoiceRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'var(--widdx-gray-50)';
            this.style.transform = 'translateX(4px)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
            this.style.transform = 'translateX(0)';
        });
    });

    // Auto-show filter panel if filters are applied
    const urlParams = new URLSearchParams(window.location.search);
    const hasFilters = urlParams.has('status') || urlParams.has('date_from') || urlParams.has('date_to');

    if (hasFilters && filterPanel && filterBtn) {
        filterPanel.style.display = 'block';
        filterBtn.classList.add('active');
    }
}

/**
 * Initialize service page functionality
 */
function initializeServicePage() {
    // Service card hover effects
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Usage bar animations
    const usageBars = document.querySelectorAll('.usage-progress');
    const usageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const bar = entry.target;
                const width = bar.style.width;
                bar.style.width = '0%';
                bar.style.transition = 'width 1s ease-out';

                setTimeout(() => {
                    bar.style.width = width;
                }, 100);

                usageObserver.unobserve(bar);
            }
        });
    }, { threshold: 0.5 });

    usageBars.forEach(bar => usageObserver.observe(bar));

    // Progress bar animations
    const progressBars = document.querySelectorAll('.progress-bar');
    const progressObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const bar = entry.target;
                const width = bar.style.width;
                bar.style.width = '0%';
                bar.style.transition = 'width 1.5s ease-out';

                setTimeout(() => {
                    bar.style.width = width;
                }, 200);

                progressObserver.unobserve(bar);
            }
        });
    }, { threshold: 0.5 });

    progressBars.forEach(bar => progressObserver.observe(bar));
}

/**
 * Initialize dashboard animations
 */
function initializeDashboardAnimations() {
    // Animate welcome stats
    const welcomeStats = document.querySelectorAll('.welcome-stat-value');
    welcomeStats.forEach((stat, index) => {
        const finalValue = stat.textContent;
        stat.textContent = '0';

        setTimeout(() => {
            animateNumber(stat, 0, parseFloat(finalValue.replace(/[^0-9.-]/g, '')), 1500, finalValue);
        }, index * 200);
    });

    // Animate dashboard cards
    const dashboardCards = document.querySelectorAll('.dashboard-card');
    dashboardCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });
}

/**
 * Animate number counting
 */
function animateNumber(element, start, end, duration, finalText) {
    const range = end - start;
    const increment = range / (duration / 16);
    let current = start;

    const timer = setInterval(() => {
        current += increment;
        if (current >= end) {
            current = end;
            clearInterval(timer);
            element.textContent = finalText;
        } else {
            element.textContent = Math.floor(current).toLocaleString();
        }
    }, 16);
}

// Initialize responsive features
document.addEventListener('DOMContentLoaded', function() {
    initializeResponsiveDashboard();

    // Page-specific initializations
    if (document.querySelector('.invoice-stats')) {
        initializeInvoicePage();
    }

    if (document.querySelector('.services-grid')) {
        initializeServicePage();
    }

    if (document.querySelector('.welcome-section')) {
        initializeDashboardAnimations();
    }
});

// Export functions for global use
window.WIDDXClients = {
    initializeClientFeatures,
    initializeDashboardAnimations,
    initializeSidebarNavigation,
    showNotification,
    sortTable
};

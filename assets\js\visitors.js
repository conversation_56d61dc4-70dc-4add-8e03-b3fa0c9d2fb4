/**
 * WIDDX Visitors JavaScript
 * Landing page animations, scroll effects, hero interactions
 */

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeVisitorFeatures();
});

/**
 * Initialize all visitor-facing features
 */
function initializeVisitorFeatures() {
    initializeScrollAnimations();
    initializeNavbarEffects();
    initializeHeroAnimations();
    initializeSmoothScrolling();
    initializeParallaxEffects();
    initializeCounterAnimations();
}

/**
 * Initialize scroll-triggered animations using Intersection Observer
 */
function initializeScrollAnimations() {
    // Create intersection observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
                
                // Add staggered animation delay for child elements
                const children = entry.target.querySelectorAll('.fade-in-up, .fade-in-left, .fade-in-right, .scale-in');
                children.forEach((child, index) => {
                    setTimeout(() => {
                        child.classList.add('animate');
                    }, index * 100);
                });
            }
        });
    }, observerOptions);

    // Observe all animation elements
    const animationElements = document.querySelectorAll('.fade-in-up, .fade-in-left, .fade-in-right, .scale-in');
    animationElements.forEach(el => observer.observe(el));

    // Observe sections for staggered animations
    const sections = document.querySelectorAll('.features-section, .pricing-section, .hero-section');
    sections.forEach(section => observer.observe(section));
}

/**
 * Initialize navbar scroll effects
 */
function initializeNavbarEffects() {
    const navbar = document.querySelector('.public-navbar');
    if (!navbar) return;

    let lastScrollY = window.scrollY;
    let ticking = false;

    function updateNavbar() {
        const scrollY = window.scrollY;
        
        // Add/remove scrolled class based on scroll position
        if (scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        // Hide/show navbar based on scroll direction
        if (scrollY > lastScrollY && scrollY > 100) {
            navbar.style.transform = 'translateY(-100%)';
        } else {
            navbar.style.transform = 'translateY(0)';
        }

        lastScrollY = scrollY;
        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateNavbar);
            ticking = true;
        }
    }

    window.addEventListener('scroll', requestTick, { passive: true });
}

/**
 * Initialize hero section animations
 */
function initializeHeroAnimations() {
    const heroTitle = document.querySelector('.hero-title');
    const heroSubtitle = document.querySelector('.hero-subtitle');
    const heroCta = document.querySelector('.hero-cta');

    if (!heroTitle) return;

    // Animate hero elements on load
    setTimeout(() => {
        heroTitle.style.opacity = '0';
        heroTitle.style.transform = 'translateY(30px)';
        heroTitle.style.transition = 'all 0.8s ease-out';
        
        setTimeout(() => {
            heroTitle.style.opacity = '1';
            heroTitle.style.transform = 'translateY(0)';
        }, 100);
    }, 200);

    if (heroSubtitle) {
        setTimeout(() => {
            heroSubtitle.style.opacity = '0';
            heroSubtitle.style.transform = 'translateY(30px)';
            heroSubtitle.style.transition = 'all 0.8s ease-out';
            
            setTimeout(() => {
                heroSubtitle.style.opacity = '1';
                heroSubtitle.style.transform = 'translateY(0)';
            }, 100);
        }, 400);
    }

    if (heroCta) {
        setTimeout(() => {
            heroCta.style.opacity = '0';
            heroCta.style.transform = 'translateY(30px)';
            heroCta.style.transition = 'all 0.8s ease-out';
            
            setTimeout(() => {
                heroCta.style.opacity = '1';
                heroCta.style.transform = 'translateY(0)';
            }, 100);
        }, 600);
    }
}

/**
 * Initialize smooth scrolling for anchor links
 */
function initializeSmoothScrolling() {
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href === '#') return;
            
            const target = document.querySelector(href);
            if (!target) return;
            
            e.preventDefault();
            
            const offsetTop = target.offsetTop - 80; // Account for fixed navbar
            
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
        });
    });
}

/**
 * Initialize parallax effects for hero section
 */
function initializeParallaxEffects() {
    const heroSection = document.querySelector('.hero-section');
    if (!heroSection) return;

    let ticking = false;

    function updateParallax() {
        const scrolled = window.pageYOffset;
        const parallaxSpeed = 0.5;
        
        heroSection.style.transform = `translateY(${scrolled * parallaxSpeed}px)`;
        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    }

    window.addEventListener('scroll', requestTick, { passive: true });
}

/**
 * Initialize animated counters
 */
function initializeCounterAnimations() {
    const counters = document.querySelectorAll('[data-counter]');
    
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => counterObserver.observe(counter));
}

/**
 * Animate a counter element
 */
function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-counter'));
    const duration = 2000; // 2 seconds
    const step = target / (duration / 16); // 60fps
    let current = 0;

    const timer = setInterval(() => {
        current += step;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current).toLocaleString();
    }, 16);
}

/**
 * Initialize mobile menu toggle
 */
function initializeMobileMenu() {
    const mobileToggle = document.querySelector('.navbar-toggler');
    const mobileMenu = document.querySelector('.navbar-collapse');
    
    if (!mobileToggle || !mobileMenu) return;

    mobileToggle.addEventListener('click', function() {
        mobileMenu.classList.toggle('show');
        this.classList.toggle('active');
        
        // Animate hamburger icon
        const icon = this.querySelector('.navbar-toggler-icon');
        if (icon) {
            icon.style.transform = this.classList.contains('active') ? 'rotate(90deg)' : 'rotate(0deg)';
        }
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!mobileToggle.contains(e.target) && !mobileMenu.contains(e.target)) {
            mobileMenu.classList.remove('show');
            mobileToggle.classList.remove('active');
            
            const icon = mobileToggle.querySelector('.navbar-toggler-icon');
            if (icon) {
                icon.style.transform = 'rotate(0deg)';
            }
        }
    });
}

/**
 * Initialize pricing card interactions
 */
function initializePricingCards() {
    const pricingCards = document.querySelectorAll('.pricing-card');
    
    pricingCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Add subtle animation on hover
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            // Reset transform, but keep featured card scaling
            if (this.classList.contains('featured')) {
                this.style.transform = 'scale(1.05)';
            } else {
                this.style.transform = 'translateY(0) scale(1)';
            }
        });
    });
}

/**
 * Initialize feature card interactions
 */
function initializeFeatureCards() {
    const featureCards = document.querySelectorAll('.feature-card');
    
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Animate icon on hover
            const icon = this.querySelector('.feature-icon');
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            // Reset icon animation
            const icon = this.querySelector('.feature-icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });
}

/**
 * Initialize page load animations
 */
function initializePageLoadAnimations() {
    // Add page load animation class to body
    document.body.classList.add('page-loaded');
    
    // Animate elements that should appear immediately
    const immediateElements = document.querySelectorAll('.hero-content > *');
    immediateElements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            el.style.transition = 'all 0.6s ease-out';
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
        }, index * 200);
    });
}

/**
 * Initialize pricing toggle functionality
 */
function initializePricingToggle() {
    const monthlyRadio = document.getElementById('monthly');
    const yearlyRadio = document.getElementById('yearly');
    const monthlyPrices = document.querySelectorAll('.monthly-price');
    const yearlyPrices = document.querySelectorAll('.yearly-price');

    if (!monthlyRadio || !yearlyRadio) return;

    function togglePricing() {
        if (yearlyRadio.checked) {
            monthlyPrices.forEach(price => {
                price.style.display = 'none';
                price.style.opacity = '0';
            });
            yearlyPrices.forEach(price => {
                price.style.display = 'block';
                setTimeout(() => price.style.opacity = '1', 50);
            });
        } else {
            yearlyPrices.forEach(price => {
                price.style.display = 'none';
                price.style.opacity = '0';
            });
            monthlyPrices.forEach(price => {
                price.style.display = 'block';
                setTimeout(() => price.style.opacity = '1', 50);
            });
        }
    }

    // Add transition styles
    [...monthlyPrices, ...yearlyPrices].forEach(price => {
        price.style.transition = 'opacity 0.3s ease-in-out';
        price.style.opacity = '1';
    });

    monthlyRadio.addEventListener('change', togglePricing);
    yearlyRadio.addEventListener('change', togglePricing);
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('form[data-validate]');

    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
            let isValid = true;

            inputs.forEach(input => {
                const errorElement = input.parentElement.querySelector('.error-message');

                // Remove existing error
                if (errorElement) {
                    errorElement.remove();
                }

                input.classList.remove('error');

                // Validate input
                if (!input.value.trim()) {
                    showFieldError(input, 'This field is required');
                    isValid = false;
                } else if (input.type === 'email' && !isValidEmail(input.value)) {
                    showFieldError(input, 'Please enter a valid email address');
                    isValid = false;
                }
            });

            if (isValid) {
                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.textContent;
                submitBtn.textContent = 'Sending...';
                submitBtn.disabled = true;

                // Simulate form submission (replace with actual submission logic)
                setTimeout(() => {
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                    showSuccessMessage(form, 'Message sent successfully!');
                    form.reset();
                }, 2000);
            }
        });
    });
}

function showFieldError(input, message) {
    input.classList.add('error');
    const errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    errorElement.textContent = message;
    errorElement.style.color = 'var(--widdx-error)';
    errorElement.style.fontSize = 'var(--widdx-font-size-sm)';
    errorElement.style.marginTop = 'var(--widdx-spacing-1)';
    input.parentElement.appendChild(errorElement);
}

function showSuccessMessage(form, message) {
    const successElement = document.createElement('div');
    successElement.className = 'success-message';
    successElement.textContent = message;
    successElement.style.color = 'var(--widdx-success)';
    successElement.style.padding = 'var(--widdx-spacing-3)';
    successElement.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';
    successElement.style.borderRadius = 'var(--widdx-radius-md)';
    successElement.style.marginTop = 'var(--widdx-spacing-4)';

    form.appendChild(successElement);

    setTimeout(() => {
        successElement.remove();
    }, 5000);
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Initialize testimonial interactions
 */
function initializeTestimonials() {
    const testimonialCards = document.querySelectorAll('.testimonial-card');

    testimonialCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Initialize additional features when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeMobileMenu();
    initializePricingCards();
    initializeFeatureCards();
    initializePageLoadAnimations();
    initializePricingToggle();
    initializeFormValidation();
    initializeTestimonials();
    initializeDashboardPreview();
    initializeScrollIndicator();
    initializeResponsiveAnimations();
});

/**
 * Initialize dashboard preview interactions
 */
function initializeDashboardPreview() {
    const dashboardPreview = document.querySelector('.dashboard-preview');
    const chartBars = document.querySelectorAll('.chart-bar');
    const actionBtns = document.querySelectorAll('.action-btn');

    if (dashboardPreview) {
        // Animate chart bars on hover
        dashboardPreview.addEventListener('mouseenter', function() {
            chartBars.forEach((bar, index) => {
                bar.style.setProperty('--i', index);
                bar.style.animationDelay = `${index * 0.1}s`;
                bar.classList.add('animate');
            });
        });

        // Action button interactions
        actionBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();

                // Add click animation
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);

                // Show notification
                showNotification('Feature coming soon!', 'info');
            });
        });
    }
}

/**
 * Initialize scroll indicator
 */
function initializeScrollIndicator() {
    const scrollIndicator = document.querySelector('.scroll-indicator');

    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            const nextSection = document.querySelector('.features-section, .services-section');
            if (nextSection) {
                nextSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });

        // Hide scroll indicator when user scrolls
        let scrollTimeout;
        window.addEventListener('scroll', function() {
            clearTimeout(scrollTimeout);

            if (window.scrollY > 100) {
                scrollIndicator.style.opacity = '0';
                scrollIndicator.style.pointerEvents = 'none';
            } else {
                scrollIndicator.style.opacity = '1';
                scrollIndicator.style.pointerEvents = 'auto';
            }
        });
    }
}

/**
 * Initialize responsive animations
 */
function initializeResponsiveAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements with animation classes
    const animatedElements = document.querySelectorAll('.fade-in-up, .fade-in-right, .fade-in-left');
    animatedElements.forEach(el => {
        observer.observe(el);
    });

    // Handle window resize for responsive adjustments
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            handleResponsiveChanges();
        }, 250);
    });
}

/**
 * Handle responsive layout changes
 */
function handleResponsiveChanges() {
    const isMobile = window.innerWidth <= 768;
    const isTablet = window.innerWidth <= 1024 && window.innerWidth > 768;

    // Adjust dashboard preview for mobile
    const dashboardPreview = document.querySelector('.dashboard-preview');
    if (dashboardPreview) {
        if (isMobile) {
            dashboardPreview.style.transform = 'none';
        } else if (isTablet) {
            dashboardPreview.style.transform = 'perspective(800px) rotateY(-3deg) rotateX(3deg)';
        } else {
            dashboardPreview.style.transform = 'perspective(1000px) rotateY(-5deg) rotateX(5deg)';
        }
    }

    // Adjust floating elements
    const floatingElements = document.querySelector('.floating-elements');
    if (floatingElements) {
        floatingElements.style.display = isMobile ? 'none' : 'block';
    }

    // Adjust particle animations for performance on mobile
    const particles = document.querySelectorAll('.particle');
    particles.forEach(particle => {
        if (isMobile) {
            particle.style.animationDuration = '12s'; // Slower on mobile
        } else {
            particle.style.animationDuration = '8s';
        }
    });
}

/**
 * Enhanced notification system
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)} me-2"></i>
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--widdx-white);
        border: 1px solid var(--widdx-gray-200);
        border-radius: var(--widdx-radius-lg);
        box-shadow: var(--widdx-shadow-lg);
        padding: var(--widdx-spacing-4);
        z-index: 9999;
        transform: translateX(100%);
        transition: transform var(--widdx-transition-normal);
        max-width: 300px;
    `;

    // Add type-specific styling
    const typeColors = {
        success: 'var(--widdx-success)',
        error: 'var(--widdx-error)',
        warning: 'var(--widdx-warning)',
        info: 'var(--widdx-info)'
    };

    notification.style.borderLeftColor = typeColors[type];
    notification.style.borderLeftWidth = '4px';

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}

function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// Export functions for global use
window.WIDDXVisitors = {
    initializeVisitorFeatures,
    initializeScrollAnimations,
    initializeNavbarEffects,
    initializeHeroAnimations,
    animateCounter
};

{# Modern Macro Functions for WIDDX Theme #}

{# Status Badge Macro #}
{% macro status_badge(status, text) %}
    {% set badge_class = 'badge' %}
    {% if status == 'active' or status == 'paid' or status == 'completed' %}
        {% set badge_class = badge_class ~ ' bg-success' %}
    {% elseif status == 'pending' or status == 'processing' %}
        {% set badge_class = badge_class ~ ' bg-warning text-dark' %}
    {% elseif status == 'suspended' or status == 'cancelled' or status == 'failed' %}
        {% set badge_class = badge_class ~ ' bg-danger' %}
    {% elseif status == 'draft' or status == 'inactive' %}
        {% set badge_class = badge_class ~ ' bg-secondary' %}
    {% else %}
        {% set badge_class = badge_class ~ ' bg-primary' %}
    {% endif %}
    
    <span class="{{ badge_class }} rounded-pill">
        {{ text|default(status|title) }}
    </span>
{% endmacro %}

{# Modern Button Macro #}
{% macro button(text, url, type, size, icon, class) %}
    {% set btn_class = 'btn' %}
    {% set btn_type = type|default('primary') %}
    {% set btn_size = size|default('') %}
    {% set btn_icon = icon|default('') %}
    {% set extra_class = class|default('') %}
    
    {% if btn_size %}
        {% set btn_class = btn_class ~ ' btn-' ~ btn_size %}
    {% endif %}
    
    {% set btn_class = btn_class ~ ' btn-' ~ btn_type %}
    
    {% if extra_class %}
        {% set btn_class = btn_class ~ ' ' ~ extra_class %}
    {% endif %}
    
    <a href="{{ url }}" class="{{ btn_class }}">
        {% if btn_icon %}
            <i class="{{ btn_icon }} me-1"></i>
        {% endif %}
        {{ text }}
    </a>
{% endmacro %}

{# Card Macro #}
{% macro card(title, content, footer, header_class, body_class, footer_class) %}
    <div class="card card-modern">
        {% if title %}
            <div class="card-header {{ header_class|default('') }}">
                <h5 class="card-title mb-0">{{ title }}</h5>
            </div>
        {% endif %}
        
        <div class="card-body {{ body_class|default('') }}">
            {{ content }}
        </div>
        
        {% if footer %}
            <div class="card-footer {{ footer_class|default('') }}">
                {{ footer }}
            </div>
        {% endif %}
    </div>
{% endmacro %}

{# Alert Macro #}
{% macro alert(message, type, dismissible, icon) %}
    {% set alert_type = type|default('info') %}
    {% set alert_icon = icon|default('') %}
    {% set is_dismissible = dismissible|default(true) %}
    
    {% if not alert_icon %}
        {% if alert_type == 'success' %}
            {% set alert_icon = 'fas fa-check-circle' %}
        {% elseif alert_type == 'danger' or alert_type == 'error' %}
            {% set alert_icon = 'fas fa-exclamation-circle' %}
        {% elseif alert_type == 'warning' %}
            {% set alert_icon = 'fas fa-exclamation-triangle' %}
        {% else %}
            {% set alert_icon = 'fas fa-info-circle' %}
        {% endif %}
    {% endif %}
    
    <div class="alert alert-{{ alert_type }}{% if is_dismissible %} alert-dismissible{% endif %} fade show animate__animated animate__fadeInDown" role="alert">
        <div class="d-flex align-items-center">
            {% if alert_icon %}
                <i class="{{ alert_icon }} me-2"></i>
            {% endif %}
            <div class="flex-grow-1">
                {{ message }}
            </div>
            {% if is_dismissible %}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            {% endif %}
        </div>
    </div>
{% endmacro %}

{# Loading Spinner Macro #}
{% macro loading_spinner(size, text) %}
    {% set spinner_size = size|default('') %}
    {% set spinner_text = text|default('Loading...') %}
    
    <div class="d-flex align-items-center justify-content-center p-3">
        <div class="spinner-border text-primary{% if spinner_size %} spinner-border-{{ spinner_size }}{% endif %}" role="status">
            <span class="visually-hidden">{{ spinner_text }}</span>
        </div>
        {% if spinner_text and spinner_text != 'Loading...' %}
            <span class="ms-2">{{ spinner_text }}</span>
        {% endif %}
    </div>
{% endmacro %}

{# Progress Bar Macro #}
{% macro progress_bar(value, max, label, color, striped, animated) %}
    {% set progress_value = value|default(0) %}
    {% set progress_max = max|default(100) %}
    {% set progress_percent = (progress_value / progress_max * 100)|round %}
    {% set progress_color = color|default('primary') %}
    {% set progress_label = label|default('') %}
    {% set is_striped = striped|default(false) %}
    {% set is_animated = animated|default(false) %}
    
    <div class="progress" style="height: 1rem;">
        <div class="progress-bar bg-{{ progress_color }}{% if is_striped %} progress-bar-striped{% endif %}{% if is_animated %} progress-bar-animated{% endif %}" 
             role="progressbar" 
             style="width: {{ progress_percent }}%" 
             aria-valuenow="{{ progress_value }}" 
             aria-valuemin="0" 
             aria-valuemax="{{ progress_max }}">
            {% if progress_label %}
                {{ progress_label }}
            {% else %}
                {{ progress_percent }}%
            {% endif %}
        </div>
    </div>
{% endmacro %}

{# Avatar Macro #}
{% macro avatar(name, size, image, color) %}
    {% set avatar_size = size|default('md') %}
    {% set avatar_color = color|default('primary') %}
    {% set avatar_name = name|default('User') %}
    {% set avatar_image = image|default('') %}
    
    <div class="avatar-{{ avatar_size }}">
        {% if avatar_image %}
            <img src="{{ avatar_image }}" alt="{{ avatar_name }}" class="rounded-circle">
        {% else %}
            <div class="avatar-initial rounded-circle bg-{{ avatar_color }} text-white">
                {% if avatar_name|length >= 2 %}
                    {{ avatar_name|split(' ')|first|first|upper }}{{ avatar_name|split(' ')|last|first|upper }}
                {% else %}
                    {{ avatar_name|first|upper }}
                {% endif %}
            </div>
        {% endif %}
    </div>
{% endmacro %}

{# Breadcrumb Macro #}
{% macro breadcrumb(items) %}
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            {% for item in items %}
                {% if loop.last %}
                    <li class="breadcrumb-item active" aria-current="page">{{ item.title }}</li>
                {% else %}
                    <li class="breadcrumb-item">
                        {% if item.url %}
                            <a href="{{ item.url }}">{{ item.title }}</a>
                        {% else %}
                            {{ item.title }}
                        {% endif %}
                    </li>
                {% endif %}
            {% endfor %}
        </ol>
    </nav>
{% endmacro %}

{# Data Table Macro #}
{% macro data_table(headers, rows, actions, striped, hover) %}
    {% set is_striped = striped|default(true) %}
    {% set is_hover = hover|default(true) %}
    
    <div class="table-responsive">
        <table class="table table-modern{% if is_striped %} table-striped{% endif %}{% if is_hover %} table-hover{% endif %}">
            {% if headers %}
                <thead>
                    <tr>
                        {% for header in headers %}
                            <th scope="col">{{ header }}</th>
                        {% endfor %}
                        {% if actions %}
                            <th scope="col" class="text-end">Actions</th>
                        {% endif %}
                    </tr>
                </thead>
            {% endif %}
            
            <tbody>
                {% for row in rows %}
                    <tr>
                        {% for cell in row %}
                            <td>{{ cell }}</td>
                        {% endfor %}
                        {% if actions %}
                            <td class="text-end">
                                {% for action in actions %}
                                    {{ action }}
                                {% endfor %}
                            </td>
                        {% endif %}
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% endmacro %}

{# Empty State Macro #}
{% macro empty_state(title, message, icon, action_text, action_url) %}
    {% set empty_title = title|default('No data found') %}
    {% set empty_message = message|default('There are no items to display at this time.') %}
    {% set empty_icon = icon|default('fas fa-inbox') %}
    
    <div class="text-center py-5">
        <div class="mb-4">
            <i class="{{ empty_icon }} fa-3x text-muted"></i>
        </div>
        <h4 class="text-muted mb-2">{{ empty_title }}</h4>
        <p class="text-muted mb-4">{{ empty_message }}</p>
        
        {% if action_text and action_url %}
            <a href="{{ action_url }}" class="btn btn-primary">
                {{ action_text }}
            </a>
        {% endif %}
    </div>
{% endmacro %}

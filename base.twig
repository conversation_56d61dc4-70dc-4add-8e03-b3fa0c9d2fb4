<!DOCTYPE html>
<html lang="{{ guest.system_locale }}" class="h-100">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="{{ settings.meta_description|default(guest.system_company.name ~ ' - Modern Billing Platform') }}">
    <meta name="keywords" content="{{ settings.meta_keywords|default('billing, hosting, services') }}">
    <meta name="author" content="{{ settings.meta_author|default(guest.system_company.name) }}">
    <meta name="robots" content="{{ settings.meta_robots|default('index, follow') }}">
    
    <!-- WIDDX Brand Meta -->
    <meta name="theme-color" content="#1E40AF">
    <meta name="msapplication-TileColor" content="#1E40AF">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ constant('FOSSBilling\\Config::getProperty', 'url') }}">
    <meta property="og:title" content="{{ settings.meta_title_prefix }}{% block meta_title %}{{ guest.system_company.name }}{% endblock %}{{ settings.meta_title_suffix }}">
    <meta property="og:description" content="{{ settings.meta_description|default(guest.system_company.name ~ ' - Modern Billing Platform') }}">
    <meta property="og:image" content="{{ 'assets/img/widdx-og-image.png'|asset_url }}">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ constant('FOSSBilling\\Config::getProperty', 'url') }}">
    <meta property="twitter:title" content="{{ settings.meta_title_prefix }}{% block meta_title %}{{ guest.system_company.name }}{% endblock %}{{ settings.meta_title_suffix }}">
    <meta property="twitter:description" content="{{ settings.meta_description|default(guest.system_company.name ~ ' - Modern Billing Platform') }}">
    <meta property="twitter:image" content="{{ 'assets/img/widdx-og-image.png'|asset_url }}">

    <title>{{ settings.meta_title_prefix }}{% block meta_title %}{{ guest.system_company.name }}{% endblock %}{{ settings.meta_title_suffix }}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ 'assets/img/favicon.ico'|asset_url }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ 'assets/img/apple-touch-icon.png'|asset_url }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ 'assets/img/favicon-32x32.png'|asset_url }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ 'assets/img/favicon-16x16.png'|asset_url }}">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">
    
    <!-- WIDDX Global CSS Framework -->
    <link rel="stylesheet" href="{{ 'assets/css/global.css'|asset_url }}">
    
    {% if client %}
        <!-- Client Dashboard Styles -->
        <link rel="stylesheet" href="{{ 'assets/css/clients.css'|asset_url }}">
    {% else %}
        <!-- Visitor/Public Styles -->
        <link rel="stylesheet" href="{{ 'assets/css/visitors.css'|asset_url }}">
    {% endif %}
    
    <!-- Custom CSS Block -->
    {% block css %}{% endblock %}
    
    <!-- Custom JavaScript Injection -->
    {% if settings.inject_javascript %}
        {{ settings.inject_javascript|raw }}
    {% endif %}
</head>

<body class="{% if client %}dashboard-layout{% else %}public-layout{% endif %} {% block body_class %}{% endblock %}">
    <!-- CSRF Token for JavaScript -->
    <meta name="csrf-token" content="{{ CSRFToken }}">
    
    {% if client %}
        <!-- Authenticated User Layout -->
        {% include 'clients/layout.twig' %}
    {% else %}
        <!-- Public/Visitor Layout -->
        {% include 'visitors/layout.twig' %}
    {% endif %}
    
    <!-- Global JavaScript -->
    <script>
        // Global WIDDX Configuration
        window.WIDDX = {
            baseUrl: '{{ constant('FOSSBilling\\Config::getProperty', 'url') }}',
            csrfToken: '{{ CSRFToken }}',
            locale: '{{ guest.system_locale }}',
            currency: '{{ guest.system_company.currency|default('USD') }}',
            isAuthenticated: {{ client ? 'true' : 'false' }},
            user: {{ client ? {
                id: client.id,
                email: client.email,
                first_name: client.first_name,
                last_name: client.last_name,
                company: client.company,
                balance: profile.balance,
                currency: profile.currency
            }|json_encode : 'false' }}
        };
        
        // Global utility functions
        window.WIDDX.formatCurrency = function(amount, currency) {
            currency = currency || window.WIDDX.currency;
            return new Intl.NumberFormat(window.WIDDX.locale, {
                style: 'currency',
                currency: currency
            }).format(amount);
        };
        
        window.WIDDX.formatDate = function(date, options) {
            options = options || { year: 'numeric', month: 'long', day: 'numeric' };
            return new Intl.DateTimeFormat(window.WIDDX.locale, options).format(new Date(date));
        };
        
        // API Helper
        window.WIDDX.api = function(endpoint, options) {
            options = options || {};
            options.headers = options.headers || {};
            options.headers['X-CSRF-TOKEN'] = window.WIDDX.csrfToken;
            options.headers['Content-Type'] = 'application/json';
            
            return fetch(window.WIDDX.baseUrl + '/api/' + endpoint, options)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                });
        };
    </script>
    
    {% if client %}
        <!-- Client Dashboard JavaScript -->
        <script src="{{ 'assets/js/clients.js'|asset_url }}"></script>
    {% else %}
        <!-- Visitor/Public JavaScript -->
        <script src="{{ 'assets/js/visitors.js'|asset_url }}"></script>
    {% endif %}
    
    <!-- Custom JavaScript Block -->
    {% block js %}{% endblock %}
    
    <!-- Performance Monitoring -->
    <script>
        // Basic performance monitoring
        window.addEventListener('load', function() {
            if ('performance' in window) {
                const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                console.log('WIDDX Theme Load Time:', loadTime + 'ms');
                
                // Send to analytics if configured
                if (window.gtag) {
                    gtag('event', 'timing_complete', {
                        name: 'load',
                        value: loadTime
                    });
                }
            }
        });
        
        // Error tracking
        window.addEventListener('error', function(e) {
            console.error('WIDDX Theme Error:', e.error);
            
            // Send to error tracking service if configured
            if (window.Sentry) {
                Sentry.captureException(e.error);
            }
        });
    </script>
</body>
</html>

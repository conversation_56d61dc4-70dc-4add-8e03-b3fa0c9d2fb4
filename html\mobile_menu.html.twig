<div class="mobile-menu">
    {% if client %}
        <!-- User Profile Section -->
        <div class="user-profile-section mb-4 p-3 bg-light rounded">
            <div class="d-flex align-items-center">
                <div class="avatar-md me-3">
                    <div class="avatar-initial rounded-circle bg-primary text-white">
                        {{ profile.first_name|first|upper }}{{ profile.last_name|first|upper }}
                    </div>
                </div>
                <div>
                    <h6 class="mb-0">
                        {% if profile.company %}
                            {{ profile.first_name }} {{ profile.last_name }} ({{ profile.company }})
                        {% else %}
                            {{ profile.first_name }} {{ profile.last_name }}
                        {% endif %}
                    </h6>
                    <small class="text-muted">{{ profile.email }}</small>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Navigation Menu -->
    <ul class="list-unstyled">
        {% if settings.side_menu_dashboard %}
            <li class="mb-1">
                <a class="d-flex align-items-center text-decoration-none text-dark p-2 rounded" href="{{ '/'|link }}">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    {{ 'Dashboard'|trans }}
                </a>
            </li>
        {% endif %}

        {% if settings.side_menu_order %}
            <li class="mb-1">
                <a class="d-flex align-items-center text-decoration-none text-dark p-2 rounded" href="{{ '/order'|link }}">
                    <i class="fas fa-shopping-cart me-2"></i>
                    {{ 'Order'|trans }}
                </a>
            </li>
        {% endif %}

        {% if settings.side_menu_support %}
            <li class="mb-1">
                <a class="d-flex align-items-center text-decoration-none text-dark p-2 rounded" href="{{ '/support'|link }}">
                    <i class="fas fa-life-ring me-2"></i>
                    {{ 'Support'|trans }}
                </a>
            </li>
        {% endif %}

        {% if settings.side_menu_services %}
            <li class="mb-1">
                <a class="d-flex align-items-center text-decoration-none text-dark p-2 rounded" href="{{ '/order/service'|link }}">
                    <i class="fas fa-server me-2"></i>
                    {{ 'Services'|trans }}
                </a>
            </li>
        {% endif %}

        {% if settings.side_menu_invoices %}
            <li class="mb-1">
                <a class="d-flex align-items-center text-decoration-none text-dark p-2 rounded" href="{{ '/invoice'|link }}">
                    <i class="fas fa-file-invoice me-2"></i>
                    {{ 'Invoices'|trans }}
                </a>
            </li>
        {% endif %}

        {% if settings.side_menu_emails %}
            <li class="mb-1">
                <a class="d-flex align-items-center text-decoration-none text-dark p-2 rounded" href="{{ '/email'|link }}">
                    <i class="fas fa-envelope me-2"></i>
                    {{ 'Email'|trans }}
                </a>
            </li>
        {% endif %}

        {% if settings.side_menu_payments %}
            <li class="mb-1">
                <a class="d-flex align-items-center text-decoration-none text-dark p-2 rounded" href="{{ '/client/balance'|link }}">
                    <i class="fas fa-wallet me-2"></i>
                    {{ 'Wallet'|trans }}
                </a>
            </li>
        {% endif %}

        {% if (guest.extension_is_on({"mod":"news"}) and settings.side_menu_news) %}
            <li class="mb-1">
                <a class="d-flex align-items-center text-decoration-none text-dark p-2 rounded" href="{{ '/news'|link }}">
                    <i class="fas fa-newspaper me-2"></i>
                    {{ 'News'|trans }}
                </a>
            </li>
        {% endif %}

        {% if (guest.support_kb_enabled() and settings.side_menu_kb) %}
            <li class="mb-1">
                <a class="d-flex align-items-center text-decoration-none text-dark p-2 rounded" href="{{ 'support/kb'|link }}">
                    <i class="fas fa-book me-2"></i>
                    {{ 'Knowledge Base'|trans }}
                </a>
            </li>
        {% endif %}

        {% if settings.sidebar_balance_enabled and client %}
            <li class="mt-3">
                <div class="balance-widget p-3 bg-light rounded">
                    <h6 class="text-muted mb-1">{{ 'Account balance'|trans }}</h6>
                    <h5 class="mb-0 text-primary">{{ profile.balance | money(profile.currency) }}</h5>
                </div>
            </li>
        {% endif %}

        {% if settings.sidebar_note_enabled %}
            <li class="mt-3">
                <div class="sidebar-note p-3 bg-info bg-opacity-10 rounded">
                    <h6 class="text-info mb-1">{{ settings.sidebar_note_title }}</h6>
                    <small class="text-muted">{{ settings.sidebar_note_content }}</small>
                </div>
            </li>
        {% endif %}
    </ul>

    {% if client %}
        <!-- User Actions -->
        <hr class="my-3">
        <ul class="list-unstyled">
            <li class="mb-1">
                <a class="d-block text-decoration-none text-dark p-2 rounded" href="{{ 'client/profile'|link }}">
                    <i class="fas fa-user me-2"></i>Profile Settings
                </a>
            </li>
            <li class="mb-1">
                <a class="d-block text-decoration-none text-danger p-2 rounded" href="{{ 'client/logout'|link }}">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </li>
        </ul>
    {% else %}
        <!-- Guest Actions -->
        <hr class="my-3">
        <div class="d-grid gap-2">
            <a class="btn btn-primary" href="{{ 'login'|link }}">
                <i class="fas fa-sign-in-alt me-1"></i>Login
            </a>
            <a class="btn btn-outline-primary" href="{{ 'signup'|link }}">
                <i class="fas fa-user-plus me-1"></i>Sign Up
            </a>
        </div>
    {% endif %}
</div>

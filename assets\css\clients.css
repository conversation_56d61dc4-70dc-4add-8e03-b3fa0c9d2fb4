/* WIDDX Clients CSS - Authenticated User Dashboard and Account Pages */
/* Dashboard, invoices, services, and other client-specific pages */

/* Dashboard Layout */
.dashboard-layout {
  min-height: 100vh;
  background: var(--widdx-gray-50);
}

.dashboard-header {
  background: var(--widdx-white);
  border-bottom: 1px solid var(--widdx-gray-200);
  box-shadow: var(--widdx-shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.dashboard-main {
  display: flex;
  min-height: calc(100vh - 80px);
}

.dashboard-sidebar {
  width: 280px;
  background: var(--widdx-white);
  border-right: 1px solid var(--widdx-gray-200);
  padding: var(--widdx-spacing-6);
  overflow-y: auto;
}

.dashboard-content {
  flex: 1;
  padding: var(--widdx-spacing-8);
  overflow-y: auto;
}

/* Welcome Section */
.welcome-section {
  background: linear-gradient(135deg, var(--widdx-primary), var(--widdx-accent));
  color: var(--widdx-white);
  padding: var(--widdx-spacing-8);
  border-radius: var(--widdx-radius-xl);
  margin-bottom: var(--widdx-spacing-8);
  position: relative;
  overflow: hidden;
}

.welcome-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/><circle cx="50" cy="50" r="25" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></svg>');
  opacity: 0.3;
}

.welcome-content {
  position: relative;
  z-index: 2;
}

.welcome-title {
  font-size: var(--widdx-font-size-3xl);
  font-weight: 700;
  margin-bottom: var(--widdx-spacing-2);
  color: var(--widdx-white);
}

.welcome-subtitle {
  font-size: var(--widdx-font-size-lg);
  opacity: 0.9;
  margin-bottom: var(--widdx-spacing-6);
  color: var(--widdx-white);
}

.welcome-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--widdx-spacing-6);
  margin-top: var(--widdx-spacing-6);
}

.welcome-stat {
  text-align: center;
}

.welcome-stat-value {
  font-size: var(--widdx-font-size-2xl);
  font-weight: 700;
  color: var(--widdx-white);
  display: block;
}

.welcome-stat-label {
  font-size: var(--widdx-font-size-sm);
  opacity: 0.8;
  color: var(--widdx-white);
}

/* Sidebar Navigation */
.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav-item {
  margin-bottom: var(--widdx-spacing-1);
}

.sidebar-nav-link {
  display: flex;
  align-items: center;
  padding: var(--widdx-spacing-3) var(--widdx-spacing-4);
  color: var(--widdx-gray-600);
  text-decoration: none;
  border-radius: var(--widdx-radius-md);
  transition: all var(--widdx-transition-fast);
  font-weight: 500;
}

.sidebar-nav-link:hover {
  background: var(--widdx-gray-100);
  color: var(--widdx-primary);
  transform: translateX(4px);
  text-decoration: none;
}

.sidebar-nav-link.active {
  background: linear-gradient(135deg, var(--widdx-primary), var(--widdx-primary-light));
  color: var(--widdx-white);
  box-shadow: var(--widdx-shadow-md);
}

.sidebar-nav-link.active:hover {
  transform: translateX(0);
  background: linear-gradient(135deg, var(--widdx-primary-dark), var(--widdx-primary));
}

.sidebar-nav-icon {
  margin-right: var(--widdx-spacing-3);
  width: 20px;
  text-align: center;
  font-size: var(--widdx-font-size-base);
}

/* Dashboard Cards */
.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--widdx-spacing-6);
  margin-bottom: var(--widdx-spacing-8);
}

.dashboard-card {
  background: var(--widdx-white);
  border-radius: var(--widdx-radius-lg);
  padding: var(--widdx-spacing-6);
  border: 1px solid var(--widdx-gray-200);
  transition: all var(--widdx-transition-normal);
  position: relative;
  overflow: hidden;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--widdx-primary), var(--widdx-accent));
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--widdx-shadow-lg);
}

.dashboard-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--widdx-spacing-4);
}

.dashboard-card-title {
  font-size: var(--widdx-font-size-lg);
  font-weight: 600;
  color: var(--widdx-gray-900);
  margin: 0;
}

.dashboard-card-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--widdx-primary), var(--widdx-accent));
  border-radius: var(--widdx-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--widdx-white);
  font-size: var(--widdx-font-size-xl);
}

.dashboard-card-value {
  font-size: var(--widdx-font-size-3xl);
  font-weight: 700;
  color: var(--widdx-primary);
  margin-bottom: var(--widdx-spacing-2);
}

.dashboard-card-label {
  color: var(--widdx-gray-600);
  font-size: var(--widdx-font-size-sm);
}

/* Data Tables */
.data-table {
  background: var(--widdx-white);
  border-radius: var(--widdx-radius-lg);
  overflow: hidden;
  box-shadow: var(--widdx-shadow-sm);
  border: 1px solid var(--widdx-gray-200);
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: var(--widdx-gray-50);
  padding: var(--widdx-spacing-4) var(--widdx-spacing-6);
  text-align: left;
  font-weight: 600;
  color: var(--widdx-gray-700);
  border-bottom: 1px solid var(--widdx-gray-200);
  font-size: var(--widdx-font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.data-table td {
  padding: var(--widdx-spacing-4) var(--widdx-spacing-6);
  border-bottom: 1px solid var(--widdx-gray-100);
  color: var(--widdx-gray-800);
}

.data-table tbody tr {
  transition: background-color var(--widdx-transition-fast);
}

.data-table tbody tr:hover {
  background: var(--widdx-gray-50);
}

.data-table tbody tr:last-child td {
  border-bottom: none;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--widdx-spacing-1) var(--widdx-spacing-3);
  border-radius: var(--widdx-radius-full);
  font-size: var(--widdx-font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.active {
  background: rgba(16, 185, 129, 0.1);
  color: var(--widdx-success);
}

.status-badge.pending {
  background: rgba(245, 158, 11, 0.1);
  color: var(--widdx-warning);
}

.status-badge.suspended {
  background: rgba(239, 68, 68, 0.1);
  color: var(--widdx-error);
}

.status-badge.paid {
  background: rgba(16, 185, 129, 0.1);
  color: var(--widdx-success);
}

.status-badge.unpaid {
  background: rgba(239, 68, 68, 0.1);
  color: var(--widdx-error);
}

/* Account Balance Widget */
.balance-widget {
  background: linear-gradient(135deg, var(--widdx-primary), var(--widdx-accent));
  color: var(--widdx-white);
  padding: var(--widdx-spacing-6);
  border-radius: var(--widdx-radius-lg);
  text-align: center;
  margin-bottom: var(--widdx-spacing-6);
  position: relative;
  overflow: hidden;
}

.balance-widget::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
}

.balance-widget-content {
  position: relative;
  z-index: 2;
}

.balance-widget-label {
  font-size: var(--widdx-font-size-sm);
  opacity: 0.9;
  margin-bottom: var(--widdx-spacing-2);
  color: var(--widdx-white);
}

.balance-widget-amount {
  font-size: var(--widdx-font-size-3xl);
  font-weight: 700;
  color: var(--widdx-white);
}

/* Service Cards */
.service-card {
  background: var(--widdx-white);
  border-radius: var(--widdx-radius-lg);
  padding: var(--widdx-spacing-6);
  border: 1px solid var(--widdx-gray-200);
  transition: all var(--widdx-transition-normal);
  margin-bottom: var(--widdx-spacing-6);
}

.service-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--widdx-shadow-lg);
  border-color: var(--widdx-primary);
}

.service-card-header {
  display: flex;
  align-items: center;
  justify-content: between;
  margin-bottom: var(--widdx-spacing-4);
  padding-bottom: var(--widdx-spacing-4);
  border-bottom: 1px solid var(--widdx-gray-200);
}

.service-card-title {
  font-size: var(--widdx-font-size-lg);
  font-weight: 600;
  color: var(--widdx-gray-900);
  margin: 0;
  flex: 1;
}

.service-card-status {
  margin-left: var(--widdx-spacing-4);
}

.service-card-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--widdx-spacing-4);
  margin-bottom: var(--widdx-spacing-6);
}

.service-detail {
  text-align: center;
}

.service-detail-label {
  font-size: var(--widdx-font-size-xs);
  color: var(--widdx-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--widdx-spacing-1);
}

.service-detail-value {
  font-size: var(--widdx-font-size-base);
  font-weight: 600;
  color: var(--widdx-gray-900);
}

.service-card-actions {
  display: flex;
  gap: var(--widdx-spacing-3);
  flex-wrap: wrap;
}

/* Invoice Cards */
.invoice-card {
  background: var(--widdx-white);
  border-radius: var(--widdx-radius-lg);
  padding: var(--widdx-spacing-6);
  border: 1px solid var(--widdx-gray-200);
  transition: all var(--widdx-transition-normal);
  margin-bottom: var(--widdx-spacing-4);
}

.invoice-card:hover {
  transform: translateY(-1px);
  box-shadow: var(--widdx-shadow-md);
}

.invoice-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--widdx-spacing-4);
}

.invoice-number {
  font-size: var(--widdx-font-size-lg);
  font-weight: 600;
  color: var(--widdx-gray-900);
}

.invoice-amount {
  font-size: var(--widdx-font-size-xl);
  font-weight: 700;
  color: var(--widdx-primary);
}

.invoice-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--widdx-spacing-4);
  margin-bottom: var(--widdx-spacing-4);
}

.invoice-detail {
  text-align: center;
}

.invoice-detail-label {
  font-size: var(--widdx-font-size-xs);
  color: var(--widdx-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--widdx-spacing-1);
}

.invoice-detail-value {
  font-size: var(--widdx-font-size-sm);
  font-weight: 500;
  color: var(--widdx-gray-800);
}

/* Responsive Design for Client Area */
/* Invoice Page Specific Styles */
.filter-panel {
  margin-bottom: var(--widdx-spacing-6);
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-form .form-label {
  font-weight: 500;
  color: var(--widdx-gray-700);
  margin-bottom: var(--widdx-spacing-2);
}

.invoice-stats {
  margin-bottom: var(--widdx-spacing-8);
}

.stat-card {
  background: var(--widdx-white);
  border: 1px solid var(--widdx-gray-200);
  border-radius: var(--widdx-radius-lg);
  padding: var(--widdx-spacing-6);
  display: flex;
  align-items: center;
  gap: var(--widdx-spacing-4);
  transition: all var(--widdx-transition-normal);
  height: 100%;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--widdx-shadow-lg);
  border-color: var(--widdx-primary);
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--widdx-primary), var(--widdx-accent));
  border-radius: var(--widdx-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--widdx-white);
  font-size: var(--widdx-font-size-xl);
}

.stat-icon.paid {
  background: linear-gradient(135deg, var(--widdx-success), #059669);
}

.stat-icon.unpaid {
  background: linear-gradient(135deg, var(--widdx-warning), #d97706);
}

.stat-icon.overdue {
  background: linear-gradient(135deg, var(--widdx-error), #dc2626);
}

.stat-value {
  font-size: var(--widdx-font-size-2xl);
  font-weight: 700;
  color: var(--widdx-gray-900);
  margin-bottom: var(--widdx-spacing-1);
}

.stat-label {
  font-size: var(--widdx-font-size-sm);
  color: var(--widdx-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.invoice-row {
  transition: all var(--widdx-transition-fast);
}

.invoice-row:hover {
  background-color: var(--widdx-gray-50);
}

.due-date.overdue {
  color: var(--widdx-error);
  font-weight: 600;
}

.tax-info {
  font-size: var(--widdx-font-size-xs);
  color: var(--widdx-gray-500);
}

.invoice-actions {
  display: flex;
  gap: var(--widdx-spacing-2);
  align-items: center;
}

.invoice-actions .btn {
  padding: var(--widdx-spacing-1) var(--widdx-spacing-2);
  font-size: var(--widdx-font-size-sm);
}

.payment-methods {
  margin-top: var(--widdx-spacing-4);
}

.payment-method {
  display: flex;
  align-items: center;
  gap: var(--widdx-spacing-3);
  padding: var(--widdx-spacing-4);
  border: 1px solid var(--widdx-gray-200);
  border-radius: var(--widdx-radius-md);
  transition: all var(--widdx-transition-fast);
  height: 100%;
}

.payment-method:hover {
  border-color: var(--widdx-primary);
  background: var(--widdx-gray-50);
}

.payment-icon {
  width: 40px;
  height: 40px;
  background: var(--widdx-gray-100);
  border-radius: var(--widdx-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--widdx-primary);
  font-size: var(--widdx-font-size-lg);
}

.payment-name {
  font-weight: 600;
  color: var(--widdx-gray-900);
  margin-bottom: var(--widdx-spacing-1);
}

.payment-description {
  font-size: var(--widdx-font-size-sm);
  color: var(--widdx-gray-500);
}

.pagination-wrapper {
  margin-top: var(--widdx-spacing-6);
  display: flex;
  justify-content: center;
}

@media (max-width: 1024px) {
  .dashboard-main {
    flex-direction: column;
  }
  
  .dashboard-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--widdx-gray-200);
  }
  
  .dashboard-content {
    padding: var(--widdx-spacing-6) var(--widdx-spacing-4);
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: var(--widdx-spacing-4) var(--widdx-spacing-3);
  }

  .page-header {
    flex-direction: column;
    gap: var(--widdx-spacing-4);
    text-align: center;
  }

  .page-header-actions {
    width: 100%;
  }

  .page-header-actions .btn {
    width: 100%;
  }

  .dashboard-cards {
    grid-template-columns: 1fr;
    gap: var(--widdx-spacing-4);
  }

  .welcome-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--widdx-spacing-3);
  }

  .welcome-stat {
    padding: var(--widdx-spacing-3);
  }

  .welcome-stat-number {
    font-size: var(--widdx-font-size-xl);
  }

  .service-card-details,
  .invoice-details {
    grid-template-columns: 1fr;
    gap: var(--widdx-spacing-2);
  }

  .service-card-actions {
    flex-direction: column;
    gap: var(--widdx-spacing-2);
  }

  .service-card-actions .btn {
    width: 100%;
  }

  /* Invoice Page Mobile */
  .invoice-stats {
    margin-bottom: var(--widdx-spacing-6);
  }

  .stat-card {
    padding: var(--widdx-spacing-4);
    flex-direction: column;
    text-align: center;
    gap: var(--widdx-spacing-3);
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: var(--widdx-font-size-lg);
  }

  .stat-value {
    font-size: var(--widdx-font-size-xl);
  }

  .filter-panel {
    padding: var(--widdx-spacing-4);
  }

  .filter-form .row {
    margin: 0;
  }

  .filter-form .col-md-3,
  .filter-form .col-md-4 {
    padding: 0 var(--widdx-spacing-1);
    margin-bottom: var(--widdx-spacing-3);
  }

  .invoice-actions {
    flex-direction: column;
    gap: var(--widdx-spacing-2);
  }

  .invoice-actions .btn {
    width: 100%;
  }

  /* Service Page Mobile */
  .services-grid {
    grid-template-columns: 1fr;
    gap: var(--widdx-spacing-4);
  }

  .service-card-header {
    padding: var(--widdx-spacing-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--widdx-spacing-3);
  }

  .service-card-body {
    padding: var(--widdx-spacing-4);
  }

  .service-card-footer {
    padding: var(--widdx-spacing-3) var(--widdx-spacing-4);
  }

  .service-actions {
    flex-direction: column;
    gap: var(--widdx-spacing-2);
  }

  .service-actions .btn,
  .service-actions .btn-group {
    width: 100%;
  }

  /* Quick Actions Mobile */
  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: var(--widdx-spacing-3);
  }

  .quick-action-card {
    flex-direction: column;
    text-align: center;
    padding: var(--widdx-spacing-4);
  }

  .quick-action-icon {
    margin-bottom: var(--widdx-spacing-2);
  }

  /* Data Tables Mobile */
  .table-responsive {
    border: none;
  }

  .table {
    font-size: var(--widdx-font-size-sm);
  }

  .table th,
  .table td {
    padding: var(--widdx-spacing-2);
    white-space: nowrap;
  }

  /* Payment Methods Mobile */
  .payment-methods .row {
    margin: 0;
  }

  .payment-methods .col-md-6 {
    padding: 0 var(--widdx-spacing-1);
    margin-bottom: var(--widdx-spacing-3);
  }

  .payment-method {
    padding: var(--widdx-spacing-3);
    flex-direction: column;
    text-align: center;
    gap: var(--widdx-spacing-2);
  }

  .payment-icon {
    width: 32px;
    height: 32px;
    font-size: var(--widdx-font-size-base);
  }
}

/* Extra Small Mobile - Clients */
@media (max-width: 480px) {
  .dashboard-content {
    padding: var(--widdx-spacing-3) var(--widdx-spacing-2);
  }

  .welcome-stats {
    grid-template-columns: 1fr;
  }

  .welcome-stat {
    padding: var(--widdx-spacing-2);
  }

  .welcome-stat-number {
    font-size: var(--widdx-font-size-lg);
  }

  .welcome-stat-label {
    font-size: var(--widdx-font-size-xs);
  }

  .stat-card {
    padding: var(--widdx-spacing-3);
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: var(--widdx-font-size-base);
  }

  .stat-value {
    font-size: var(--widdx-font-size-lg);
  }

  .service-card-header,
  .service-card-body,
  .service-card-footer {
    padding: var(--widdx-spacing-3);
  }

  .quick-action-card {
    padding: var(--widdx-spacing-3);
  }

  .quick-action-icon {
    width: 40px;
    height: 40px;
    font-size: var(--widdx-font-size-base);
  }

  .table th,
  .table td {
    padding: var(--widdx-spacing-1);
    font-size: var(--widdx-font-size-xs);
  }
}

{% extends "base.twig" %}

{% block meta_title %}{{ 'Pricing'|trans }}{% endblock %}

{% block breadcrumb %}
    <li class="breadcrumb-item active" aria-current="page">{{ 'Pricing'|trans }}</li>
{% endblock %}

{% block content %}
    <!-- Pricing Hero Section -->
    <section class="pricing-hero-section py-5 bg-light">
        <div class="container">
            <div class="text-center fade-in-up">
                <h1 class="display-4 fw-bold mb-4">{{ 'Simple, Transparent Pricing'|trans }}</h1>
                <p class="lead mb-5 text-muted">
                    {{ 'Choose the perfect plan for your needs. No hidden fees, no surprises.'|trans }}
                </p>
                
                <!-- Billing Toggle -->
                <div class="billing-toggle mb-5">
                    <div class="btn-group" role="group" aria-label="Billing period">
                        <input type="radio" class="btn-check" name="billing" id="monthly" checked>
                        <label class="btn btn-outline-primary" for="monthly">{{ 'Monthly'|trans }}</label>
                        
                        <input type="radio" class="btn-check" name="billing" id="yearly">
                        <label class="btn btn-outline-primary" for="yearly">
                            {{ 'Yearly'|trans }}
                            <span class="badge bg-success ms-2">{{ 'Save 20%'|trans }}</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Plans Section -->
    <section class="pricing-section">
        <div class="container">
            <div class="pricing-grid">
                <!-- Starter Plan -->
                <div class="pricing-card fade-in-left">
                    <div class="pricing-header">
                        <h3 class="pricing-title">{{ 'Starter'|trans }}</h3>
                        <p class="pricing-description">{{ 'Perfect for small websites and blogs'|trans }}</p>
                    </div>
                    
                    <div class="pricing-price-section">
                        <div class="pricing-price monthly-price">
                            <span class="currency">$</span>
                            <span class="amount">9.99</span>
                            <span class="period">/{{ 'month'|trans }}</span>
                        </div>
                        <div class="pricing-price yearly-price" style="display: none;">
                            <span class="currency">$</span>
                            <span class="amount">95.90</span>
                            <span class="period">/{{ 'year'|trans }}</span>
                        </div>
                    </div>
                    
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> {{ '10 GB SSD Storage'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ '100 GB Bandwidth'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ '1 Website'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Free SSL Certificate'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ '24/7 Support'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Daily Backups'|trans }}</li>
                    </ul>
                    
                    <div class="pricing-action">
                        <a href="{{ '/order'|link }}" class="btn btn-outline-primary btn-lg w-100">
                            {{ 'Get Started'|trans }}
                        </a>
                    </div>
                </div>

                <!-- Professional Plan (Featured) -->
                <div class="pricing-card featured fade-in-up">
                    <div class="pricing-header">
                        <h3 class="pricing-title">{{ 'Professional'|trans }}</h3>
                        <p class="pricing-description">{{ 'Ideal for growing businesses'|trans }}</p>
                    </div>
                    
                    <div class="pricing-price-section">
                        <div class="pricing-price monthly-price">
                            <span class="currency">$</span>
                            <span class="amount">19.99</span>
                            <span class="period">/{{ 'month'|trans }}</span>
                        </div>
                        <div class="pricing-price yearly-price" style="display: none;">
                            <span class="currency">$</span>
                            <span class="amount">191.90</span>
                            <span class="period">/{{ 'year'|trans }}</span>
                        </div>
                    </div>
                    
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> {{ '50 GB SSD Storage'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Unlimited Bandwidth'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ '5 Websites'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Free SSL Certificate'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Priority Support'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Daily Backups'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Free Domain (1 year)'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Email Accounts'|trans }}</li>
                    </ul>
                    
                    <div class="pricing-action">
                        <a href="{{ '/order'|link }}" class="btn btn-primary btn-lg w-100">
                            {{ 'Get Started'|trans }}
                        </a>
                    </div>
                </div>

                <!-- Enterprise Plan -->
                <div class="pricing-card fade-in-right">
                    <div class="pricing-header">
                        <h3 class="pricing-title">{{ 'Enterprise'|trans }}</h3>
                        <p class="pricing-description">{{ 'For large-scale applications'|trans }}</p>
                    </div>
                    
                    <div class="pricing-price-section">
                        <div class="pricing-price monthly-price">
                            <span class="currency">$</span>
                            <span class="amount">49.99</span>
                            <span class="period">/{{ 'month'|trans }}</span>
                        </div>
                        <div class="pricing-price yearly-price" style="display: none;">
                            <span class="currency">$</span>
                            <span class="amount">479.90</span>
                            <span class="period">/{{ 'year'|trans }}</span>
                        </div>
                    </div>
                    
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> {{ '200 GB SSD Storage'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Unlimited Bandwidth'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Unlimited Websites'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Free SSL Certificate'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Dedicated Support'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Hourly Backups'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Free Domain (1 year)'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Unlimited Email Accounts'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Advanced Security'|trans }}</li>
                        <li><i class="fas fa-check"></i> {{ 'Performance Monitoring'|trans }}</li>
                    </ul>
                    
                    <div class="pricing-action">
                        <a href="{{ '/order'|link }}" class="btn btn-outline-primary btn-lg w-100">
                            {{ 'Get Started'|trans }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Comparison Section -->
    <section class="features-comparison-section py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5 fade-in-up">
                <h2 class="section-title">{{ 'Compare Features'|trans }}</h2>
                <p class="section-subtitle">
                    {{ 'See what\'s included in each plan'|trans }}
                </p>
            </div>
            
            <div class="comparison-table-wrapper fade-in-up">
                <div class="table-responsive">
                    <table class="table comparison-table">
                        <thead>
                            <tr>
                                <th>{{ 'Features'|trans }}</th>
                                <th class="text-center">{{ 'Starter'|trans }}</th>
                                <th class="text-center featured-column">{{ 'Professional'|trans }}</th>
                                <th class="text-center">{{ 'Enterprise'|trans }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{{ 'SSD Storage'|trans }}</td>
                                <td class="text-center">10 GB</td>
                                <td class="text-center featured-column">50 GB</td>
                                <td class="text-center">200 GB</td>
                            </tr>
                            <tr>
                                <td>{{ 'Bandwidth'|trans }}</td>
                                <td class="text-center">100 GB</td>
                                <td class="text-center featured-column">{{ 'Unlimited'|trans }}</td>
                                <td class="text-center">{{ 'Unlimited'|trans }}</td>
                            </tr>
                            <tr>
                                <td>{{ 'Websites'|trans }}</td>
                                <td class="text-center">1</td>
                                <td class="text-center featured-column">5</td>
                                <td class="text-center">{{ 'Unlimited'|trans }}</td>
                            </tr>
                            <tr>
                                <td>{{ 'Email Accounts'|trans }}</td>
                                <td class="text-center"><i class="fas fa-times text-muted"></i></td>
                                <td class="text-center featured-column"><i class="fas fa-check text-success"></i></td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                            </tr>
                            <tr>
                                <td>{{ 'Free Domain'|trans }}</td>
                                <td class="text-center"><i class="fas fa-times text-muted"></i></td>
                                <td class="text-center featured-column"><i class="fas fa-check text-success"></i></td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                            </tr>
                            <tr>
                                <td>{{ 'SSL Certificate'|trans }}</td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                <td class="text-center featured-column"><i class="fas fa-check text-success"></i></td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                            </tr>
                            <tr>
                                <td>{{ 'Support Level'|trans }}</td>
                                <td class="text-center">24/7</td>
                                <td class="text-center featured-column">{{ 'Priority'|trans }}</td>
                                <td class="text-center">{{ 'Dedicated'|trans }}</td>
                            </tr>
                            <tr>
                                <td>{{ 'Backup Frequency'|trans }}</td>
                                <td class="text-center">{{ 'Daily'|trans }}</td>
                                <td class="text-center featured-column">{{ 'Daily'|trans }}</td>
                                <td class="text-center">{{ 'Hourly'|trans }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section py-5">
        <div class="container">
            <div class="text-center mb-5 fade-in-up">
                <h2 class="section-title">{{ 'Frequently Asked Questions'|trans }}</h2>
                <p class="section-subtitle">
                    {{ 'Got questions? We have answers.'|trans }}
                </p>
            </div>
            
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="accordion" id="pricingFAQ">
                        <div class="accordion-item fade-in-up">
                            <h3 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    {{ 'Can I upgrade or downgrade my plan?'|trans }}
                                </button>
                            </h3>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#pricingFAQ">
                                <div class="accordion-body">
                                    {{ 'Yes, you can upgrade or downgrade your plan at any time. Changes will be prorated and reflected in your next billing cycle.'|trans }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item fade-in-up">
                            <h3 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    {{ 'Is there a money-back guarantee?'|trans }}
                                </button>
                            </h3>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#pricingFAQ">
                                <div class="accordion-body">
                                    {{ 'We offer a 30-day money-back guarantee. If you\'re not satisfied with our service, we\'ll refund your payment in full.'|trans }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item fade-in-up">
                            <h3 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    {{ 'What payment methods do you accept?'|trans }}
                                </button>
                            </h3>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#pricingFAQ">
                                <div class="accordion-body">
                                    {{ 'We accept all major credit cards, PayPal, and bank transfers. All payments are processed securely.'|trans }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item fade-in-up">
                            <h3 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                    {{ 'Do you offer custom enterprise solutions?'|trans }}
                                </button>
                            </h3>
                            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#pricingFAQ">
                                <div class="accordion-body">
                                    {{ 'Yes, we offer custom enterprise solutions for large organizations. Contact our sales team to discuss your specific requirements.'|trans }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section py-5">
        <div class="container">
            <div class="text-center fade-in-up">
                <h2 class="cta-title">{{ 'Ready to Get Started?'|trans }}</h2>
                <p class="cta-subtitle">
                    {{ 'Choose your plan and start building your online presence today'|trans }}
                </p>
                <div class="cta-buttons">
                    <a href="{{ '/order'|link }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-rocket me-2"></i>
                        {{ 'Start Now'|trans }}
                    </a>
                    <a href="{{ '/contact'|link }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-comments me-2"></i>
                        {{ 'Contact Sales'|trans }}
                    </a>
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Billing toggle functionality
        const monthlyRadio = document.getElementById('monthly');
        const yearlyRadio = document.getElementById('yearly');
        const monthlyPrices = document.querySelectorAll('.monthly-price');
        const yearlyPrices = document.querySelectorAll('.yearly-price');
        
        function togglePricing() {
            if (yearlyRadio.checked) {
                monthlyPrices.forEach(price => price.style.display = 'none');
                yearlyPrices.forEach(price => price.style.display = 'block');
            } else {
                monthlyPrices.forEach(price => price.style.display = 'block');
                yearlyPrices.forEach(price => price.style.display = 'none');
            }
        }
        
        monthlyRadio.addEventListener('change', togglePricing);
        yearlyRadio.addEventListener('change', togglePricing);
        
        // Animate pricing cards on scroll
        const pricingCards = document.querySelectorAll('.pricing-card');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 200);
                }
            });
        });
        
        pricingCards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease-out';
            observer.observe(card);
        });
    });
</script>
{% endblock %}

<!DOCTYPE html>
<html lang="en" data-bs-theme="{{ settings.theme|default('light') }}">
<head>
    <meta charset="utf-8">
    <title>{{ settings.meta_title_prefix }} {% block meta_title %}{% endblock %} {{ settings.meta_title_suffix }}</title>

    <meta property="bb:url" content="{{ constant('SYSTEM_URL') }}">
    <meta property="bb:client_area" content="{{ '/'|link }}">
    <meta name="csrf-token" content="{{ CSRFToken }}">

    <meta name="description" content="{% block meta_description %}{{ settings.meta_description }}{% endblock %}">
    <meta name="keywords" content="{{ settings.meta_keywords }}">
    <meta name="robots" content="{{ settings.meta_robots }}">
    <meta name="author" content="{{ settings.meta_author }}">
    <meta name="generator" content="FOSSBilling">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    {% block opengraph %}{% endblock %}
    
    <!-- Modern CSS and Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <!-- WIDDX Theme Assets -->
    <link rel='stylesheet' type='text/css' href="{{ 'css/widdx.css' | asset_url }}">

    <link rel="shortcut icon" href="{{ guest.system_company.favicon_url }}">

    {{ "Api/API.js" | library_url | script_tag }}
    <script src="{{ 'js/widdx.js' | asset_url }}"></script>

    {{ DebugBar_renderHead() }}

    {% block head %}{% endblock %}
    {% block js %}{% endblock %}
</head>

<body class="{% block body_class %}public-page{% endblock %}">

{% block body %}

<!-- Hero Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-transparent position-absolute w-100" style="z-index: 1000;">
    <div class="container">
        
        <!-- Brand Logo -->
        <a class="navbar-brand d-flex align-items-center" href="{{ '/'|link }}">
            {% if guest.system_company.logo_url %}
                <img src="{{ guest.system_company.logo_url }}" alt="{{ guest.system_company.name }}" class="navbar-logo me-2">
            {% endif %}
            <span class="fw-bold">{{ guest.system_company.name }}</span>
        </a>

        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="{{ '/'|link }}">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ 'order'|link }}">Services</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ 'support'|link }}">Support</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ 'news'|link }}">News</a>
                </li>
            </ul>
            
            <div class="navbar-nav">
                <a class="nav-link btn btn-outline-light btn-sm me-2" href="{{ 'login'|link }}">
                    <i class="fas fa-sign-in-alt me-1"></i>Login
                </a>
                <a class="nav-link btn btn-light btn-sm text-primary" href="{{ 'signup'|link }}">
                    <i class="fas fa-user-plus me-1"></i>Get Started
                </a>
            </div>
        </div>
    </div>
</nav>

<!-- Main Content -->
<main class="main-content">
    {% include 'partial_pending_messages.html.twig' %}
    
    {% block content %}
        <div class="container py-5">
            {% include 'partial_message.html.twig' %}
            {% block content_body %}{% endblock %}
        </div>
    {% endblock %}
</main>

<!-- Modern Footer -->
<footer class="footer bg-dark text-light py-5 mt-auto">
    <div class="container">
        <div class="row">
            <div class="col-lg-4 mb-4">
                <h5 class="fw-bold mb-3">{{ guest.system_company.name }}</h5>
                <p class="text-muted">{{ guest.system_company.note|default('Your trusted hosting provider') }}</p>
                {% if guest.system_company.email %}
                    <p class="mb-1">
                        <i class="fas fa-envelope me-2"></i>
                        <a href="mailto:{{ guest.system_company.email }}" class="text-light text-decoration-none">
                            {{ guest.system_company.email }}
                        </a>
                    </p>
                {% endif %}
                {% if guest.system_company.tel %}
                    <p class="mb-0">
                        <i class="fas fa-phone me-2"></i>
                        <a href="tel:{{ guest.system_company.tel }}" class="text-light text-decoration-none">
                            {{ guest.system_company.tel }}
                        </a>
                    </p>
                {% endif %}
            </div>
            
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="fw-bold mb-3">Services</h6>
                <ul class="list-unstyled">
                    <li><a href="{{ 'order'|link }}" class="text-muted text-decoration-none">Order Services</a></li>
                    <li><a href="{{ 'client'|link }}" class="text-muted text-decoration-none">Client Area</a></li>
                    <li><a href="{{ 'support'|link }}" class="text-muted text-decoration-none">Support</a></li>
                </ul>
            </div>
            
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="fw-bold mb-3">Company</h6>
                <ul class="list-unstyled">
                    <li><a href="{{ 'page/about-us'|link }}" class="text-muted text-decoration-none">About Us</a></li>
                    <li><a href="{{ 'news'|link }}" class="text-muted text-decoration-none">News</a></li>
                    <li><a href="{{ 'page/contact-us'|link }}" class="text-muted text-decoration-none">Contact</a></li>
                </ul>
            </div>
            
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="fw-bold mb-3">Legal</h6>
                <ul class="list-unstyled">
                    <li><a href="{{ 'tos'|link }}" class="text-muted text-decoration-none">Terms of Service</a></li>
                    <li><a href="{{ 'privacy-policy'|link }}" class="text-muted text-decoration-none">Privacy Policy</a></li>
                </ul>
            </div>
            
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="fw-bold mb-3">Follow Us</h6>
                <div class="d-flex gap-2">
                    <a href="#" class="btn btn-outline-light btn-sm">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="btn btn-outline-light btn-sm">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="btn btn-outline-light btn-sm">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <hr class="my-4 border-secondary">
        
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0 text-muted">
                    &copy; {{ "now"|date("Y") }} {{ guest.system_company.name }}. All rights reserved.
                </p>
            </div>
            <div class="col-md-6 text-md-end">
                <p class="mb-0 text-muted">
                    Powered by <a href="https://fossbilling.org" class="text-light text-decoration-none">FOSSBilling</a>
                </p>
            </div>
        </div>
    </div>
</footer>

{% endblock %}

{{ DebugBar_render() }}

</body>
</html>

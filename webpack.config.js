const Encore = require('@symfony/webpack-encore');

// Manually configure the runtime environment if not already configured yet by the "encore" command.
// It's useful when you use tools that rely on webpack.config.js file.
if (!Encore.isRuntimeEnvironmentConfigured()) {
  Encore.configureRuntimeEnvironment(process.env.NODE_ENV || 'dev');
}

Encore
  .setOutputPath('./build/')
  .setPublicPath('/themes/widdx/build')

  .configureFilenames( {
    js: 'js/[name]-bundle.[contenthash:6].js',
    css: 'css/[name]-bundle.[contenthash:6].css'
  })

  .addEntry('widdx', './assets/js/widdx.js')
  .addStyleEntry('widdx-css', './assets/css/widdx.css')

  .disableSingleRuntimeChunk()
  .cleanupOutputBeforeBuild()
  .enableSourceMaps(!Encore.isProduction())
  .enableVersioning(Encore.isProduction())
  .configureBabelPresetEnv((config) => {
    config.useBuiltIns = 'usage';
    config.corejs = 3;
  })

  // Enable SASS/SCSS support
  .enableSassLoader()

  // Enable PostCSS support
  .enablePostCssLoader()

  // Copy images
  .copyFiles({
    from: './assets/img',
    to: 'images/[path][name].[hash:8].[ext]'
  })

module.exports = Encore.getWebpackConfig();

{# Modern Pricing Component for WIDDX Theme #}

{% macro pricing_card(product, period, featured) %}
    {% set is_featured = featured|default(false) %}
    {% set pricing_period = period|default('monthly') %}
    
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card pricing-card h-100{% if is_featured %} featured{% endif %}">
            {% if is_featured %}
                <div class="pricing-badge">
                    <span class="badge bg-primary">Most Popular</span>
                </div>
            {% endif %}
            
            <div class="card-header text-center border-0 bg-transparent">
                <h4 class="card-title">{{ product.title }}</h4>
                {% if product.description %}
                    <p class="text-muted">{{ product.description }}</p>
                {% endif %}
            </div>
            
            <div class="card-body text-center">
                <div class="pricing-price mb-4">
                    {% if product.pricing[pricing_period] %}
                        {% set price = product.pricing[pricing_period] %}
                        <div class="price-amount">
                            <span class="currency">{{ price.currency }}</span>
                            <span class="amount">{{ price.price|number_format(2) }}</span>
                            <span class="period">/ {{ pricing_period|slice(0, -2) }}</span>
                        </div>
                        
                        {% if price.setup_price and price.setup_price > 0 %}
                            <div class="setup-fee text-muted">
                                <small>+ {{ price.currency }}{{ price.setup_price|number_format(2) }} setup</small>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="price-amount">
                            <span class="amount">Contact Us</span>
                        </div>
                    {% endif %}
                </div>
                
                {% if product.features %}
                    <ul class="list-unstyled pricing-features">
                        {% for feature in product.features %}
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                {{ feature }}
                            </li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>
            
            <div class="card-footer border-0 bg-transparent text-center">
                {% if product.pricing[pricing_period] %}
                    <a href="{{ ('order/' ~ product.slug)|link }}" 
                       class="btn {% if is_featured %}btn-primary{% else %}btn-outline-primary{% endif %} btn-lg w-100">
                        <i class="fas fa-shopping-cart me-1"></i>
                        Order Now
                    </a>
                {% else %}
                    <a href="{{ 'contact'|link }}" 
                       class="btn btn-outline-primary btn-lg w-100">
                        <i class="fas fa-envelope me-1"></i>
                        Contact Sales
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
{% endmacro %}

{% macro pricing_table(products, periods) %}
    {% set pricing_periods = periods|default(['monthly', 'annually']) %}
    
    <div class="pricing-section">
        <!-- Period Toggle -->
        {% if pricing_periods|length > 1 %}
            <div class="text-center mb-5">
                <div class="btn-group pricing-toggle" role="group" aria-label="Pricing periods">
                    {% for period in pricing_periods %}
                        <input type="radio" class="btn-check" name="pricing-period" 
                               id="period-{{ period }}" value="{{ period }}"
                               {% if loop.first %}checked{% endif %}>
                        <label class="btn btn-outline-primary" for="period-{{ period }}">
                            {{ period|title }}
                            {% if period == 'annually' %}
                                <span class="badge bg-success ms-1">Save 20%</span>
                            {% endif %}
                        </label>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
        
        <!-- Pricing Cards -->
        <div class="row justify-content-center" id="pricing-cards">
            {% for product in products %}
                {% set is_featured = loop.index == 2 %}
                {{ _self.pricing_card(product, pricing_periods|first, is_featured) }}
            {% endfor %}
        </div>
        
        <!-- Additional Information -->
        <div class="row mt-5">
            <div class="col-12 text-center">
                <div class="pricing-info">
                    <h5 class="mb-3">All plans include:</h5>
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <i class="fas fa-shield-alt text-success me-2"></i>
                            99.9% Uptime SLA
                        </div>
                        <div class="col-md-3 mb-2">
                            <i class="fas fa-headset text-success me-2"></i>
                            24/7 Support
                        </div>
                        <div class="col-md-3 mb-2">
                            <i class="fas fa-lock text-success me-2"></i>
                            Free SSL Certificate
                        </div>
                        <div class="col-md-3 mb-2">
                            <i class="fas fa-undo text-success me-2"></i>
                            30-Day Money Back
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endmacro %}

<style>
.pricing-card {
    position: relative;
    transition: all 0.3s ease-in-out;
    border: 2px solid transparent;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.pricing-card.featured {
    border-color: var(--bs-primary);
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.pricing-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.pricing-price {
    position: relative;
}

.price-amount {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    color: var(--bs-primary);
}

.price-amount .currency {
    font-size: 1.5rem;
    vertical-align: top;
}

.price-amount .period {
    font-size: 1rem;
    color: var(--bs-secondary);
    font-weight: 400;
}

.pricing-features {
    text-align: left;
    max-width: 250px;
    margin: 0 auto;
}

.pricing-features li {
    padding: 0.25rem 0;
}

.pricing-toggle .btn {
    border-radius: 50px;
}

.pricing-info {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid #e2e8f0;
}

@media (max-width: 768px) {
    .pricing-card.featured {
        transform: none;
    }
    
    .pricing-card.featured:hover {
        transform: translateY(-5px);
    }
    
    .price-amount {
        font-size: 2rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle pricing period toggle
    const periodInputs = document.querySelectorAll('input[name="pricing-period"]');
    
    periodInputs.forEach(input => {
        input.addEventListener('change', function() {
            const selectedPeriod = this.value;
            updatePricingDisplay(selectedPeriod);
        });
    });
    
    function updatePricingDisplay(period) {
        // This would typically make an AJAX call to get updated pricing
        // For now, we'll just add a loading state
        const pricingCards = document.getElementById('pricing-cards');
        if (pricingCards) {
            pricingCards.style.opacity = '0.6';
            
            setTimeout(() => {
                pricingCards.style.opacity = '1';
            }, 300);
        }
    }
});
</script>

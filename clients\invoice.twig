{% extends "base.twig" %}

{% block meta_title %}{{ 'Invoices'|trans }}{% endblock %}

{% block breadcrumb %}
    <li class="breadcrumb-item active" aria-current="page">{{ 'Invoices'|trans }}</li>
{% endblock %}

{% block content %}
    <!-- Page Header -->
    <div class="page-header fade-in-up">
        <div class="page-header-content">
            <h1 class="page-title">{{ 'Invoices'|trans }}</h1>
            <p class="page-subtitle">{{ 'Manage your billing and payment history'|trans }}</p>
        </div>
        <div class="page-header-actions">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" id="filterBtn">
                    <i class="fas fa-filter me-2"></i>
                    {{ 'Filter'|trans }}
                </button>
                <button type="button" class="btn btn-outline-primary" id="exportBtn">
                    <i class="fas fa-download me-2"></i>
                    {{ 'Export'|trans }}
                </button>
            </div>
        </div>
    </div>

    <!-- Filter Panel -->
    <div class="filter-panel" id="filterPanel" style="display: none;">
        <div class="card">
            <div class="card-body">
                <form method="get" class="filter-form">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">{{ 'Status'|trans }}</label>
                            <select name="status" class="form-select">
                                <option value="">{{ 'All Statuses'|trans }}</option>
                                <option value="paid" {{ request.status == 'paid' ? 'selected' : '' }}>{{ 'Paid'|trans }}</option>
                                <option value="unpaid" {{ request.status == 'unpaid' ? 'selected' : '' }}>{{ 'Unpaid'|trans }}</option>
                                <option value="refunded" {{ request.status == 'refunded' ? 'selected' : '' }}>{{ 'Refunded'|trans }}</option>
                                <option value="canceled" {{ request.status == 'canceled' ? 'selected' : '' }}>{{ 'Canceled'|trans }}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">{{ 'Date From'|trans }}</label>
                            <input type="date" name="date_from" class="form-control" value="{{ request.date_from }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">{{ 'Date To'|trans }}</label>
                            <input type="date" name="date_to" class="form-control" value="{{ request.date_to }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>
                                    {{ 'Apply Filter'|trans }}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Invoice Statistics -->
    <div class="invoice-stats fade-in-up">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ invoice_stats.total|default(0) }}</div>
                        <div class="stat-label">{{ 'Total Invoices'|trans }}</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-icon paid">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ invoice_stats.paid_amount|money(profile.currency) }}</div>
                        <div class="stat-label">{{ 'Total Paid'|trans }}</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-icon unpaid">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ invoice_stats.unpaid_amount|money(profile.currency) }}</div>
                        <div class="stat-label">{{ 'Outstanding'|trans }}</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-icon overdue">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ invoice_stats.overdue|default(0) }}</div>
                        <div class="stat-label">{{ 'Overdue'|trans }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoices Table -->
    <div class="card fade-in-up">
        <div class="card-header">
            <h4 class="mb-0">{{ 'Invoice History'|trans }}</h4>
        </div>
        <div class="card-body">
            {% if invoices %}
                <div class="data-table">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>{{ 'Invoice #'|trans }}</th>
                                    <th>{{ 'Date'|trans }}</th>
                                    <th>{{ 'Due Date'|trans }}</th>
                                    <th>{{ 'Amount'|trans }}</th>
                                    <th>{{ 'Status'|trans }}</th>
                                    <th>{{ 'Actions'|trans }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in invoices %}
                                    <tr class="invoice-row" data-invoice-id="{{ invoice.id }}">
                                        <td>
                                            <div class="invoice-number">
                                                <strong>#{{ invoice.serie_nr }}</strong>
                                                {% if invoice.notes %}
                                                    <div class="invoice-notes">{{ invoice.notes|truncate(50) }}</div>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <span class="invoice-date">{{ invoice.created_at|date('M j, Y') }}</span>
                                        </td>
                                        <td>
                                            <span class="due-date {{ invoice.due_at|date('U') < 'now'|date('U') and invoice.status != 'paid' ? 'overdue' : '' }}">
                                                {{ invoice.due_at|date('M j, Y') }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="invoice-amount">
                                                <span class="amount">{{ invoice.total|money(invoice.currency) }}</span>
                                                {% if invoice.tax_rate > 0 %}
                                                    <div class="tax-info">{{ 'incl. tax'|trans }}</div>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <span class="status-badge {{ invoice.status }}">
                                                {{ invoice.status|title }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="invoice-actions">
                                                <a href="{{ '/invoice'|link }}/{{ invoice.hash }}" 
                                                   class="btn btn-outline-primary btn-sm" 
                                                   title="{{ 'View Invoice'|trans }}">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ '/invoice'|link }}/{{ invoice.hash }}/pdf" 
                                                   class="btn btn-outline-secondary btn-sm" 
                                                   title="{{ 'Download PDF'|trans }}"
                                                   target="_blank">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                {% if invoice.status == 'unpaid' %}
                                                    <a href="{{ '/invoice'|link }}/{{ invoice.hash }}/pay" 
                                                       class="btn btn-success btn-sm" 
                                                       title="{{ 'Pay Now'|trans }}">
                                                        <i class="fas fa-credit-card"></i>
                                                    </a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                {% if pagination %}
                    <div class="pagination-wrapper">
                        {{ pagination|raw }}
                    </div>
                {% endif %}
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-file-invoice fa-3x text-muted mb-4"></i>
                    <h4 class="text-muted">{{ 'No Invoices Found'|trans }}</h4>
                    <p class="text-muted">{{ 'You don\'t have any invoices yet. When you place an order, invoices will appear here.'|trans }}</p>
                    <a href="{{ '/order'|link }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        {{ 'Place Your First Order'|trans }}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Payment Methods (if unpaid invoices exist) -->
    {% if has_unpaid_invoices %}
        <div class="card fade-in-up">
            <div class="card-header">
                <h4 class="mb-0">{{ 'Payment Methods'|trans }}</h4>
            </div>
            <div class="card-body">
                <div class="payment-methods">
                    <div class="row">
                        {% if guest.extension_is_on({"mod":"invoice","type":"gateway","id":"PayPal"}) %}
                            <div class="col-md-4 mb-3">
                                <div class="payment-method">
                                    <div class="payment-icon">
                                        <i class="fab fa-paypal"></i>
                                    </div>
                                    <div class="payment-info">
                                        <div class="payment-name">PayPal</div>
                                        <div class="payment-description">{{ 'Pay securely with PayPal'|trans }}</div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                        
                        {% if guest.extension_is_on({"mod":"invoice","type":"gateway","id":"Stripe"}) %}
                            <div class="col-md-4 mb-3">
                                <div class="payment-method">
                                    <div class="payment-icon">
                                        <i class="fab fa-cc-stripe"></i>
                                    </div>
                                    <div class="payment-info">
                                        <div class="payment-name">{{ 'Credit Card'|trans }}</div>
                                        <div class="payment-description">{{ 'Pay with credit or debit card'|trans }}</div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                        
                        {% if guest.extension_is_on({"mod":"invoice","type":"gateway","id":"BankTransfer"}) %}
                            <div class="col-md-4 mb-3">
                                <div class="payment-method">
                                    <div class="payment-icon">
                                        <i class="fas fa-university"></i>
                                    </div>
                                    <div class="payment-info">
                                        <div class="payment-name">{{ 'Bank Transfer'|trans }}</div>
                                        <div class="payment-description">{{ 'Pay via bank transfer'|trans }}</div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filter toggle
        const filterBtn = document.getElementById('filterBtn');
        const filterPanel = document.getElementById('filterPanel');
        
        if (filterBtn && filterPanel) {
            filterBtn.addEventListener('click', function() {
                if (filterPanel.style.display === 'none') {
                    filterPanel.style.display = 'block';
                    filterBtn.classList.add('active');
                } else {
                    filterPanel.style.display = 'none';
                    filterBtn.classList.remove('active');
                }
            });
        }
        
        // Export functionality
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', function() {
                // Add export functionality here
                window.location.href = '{{ "/invoice/export"|link }}';
            });
        }
        
        // Row hover effects
        const invoiceRows = document.querySelectorAll('.invoice-row');
        invoiceRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'var(--widdx-gray-50)';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
        
        // Auto-hide filter panel if no filters are applied
        const urlParams = new URLSearchParams(window.location.search);
        const hasFilters = urlParams.has('status') || urlParams.has('date_from') || urlParams.has('date_to');
        
        if (hasFilters && filterPanel) {
            filterPanel.style.display = 'block';
            filterBtn.classList.add('active');
        }
    });
</script>
{% endblock %}

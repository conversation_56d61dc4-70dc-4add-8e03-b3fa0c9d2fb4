{% extends "layout_public.html.twig" %}

{% block meta_title %}Error {{ code }}{% endblock %}

{% block body_class %}error-page{% endblock %}

{% block content %}
<div class="error-container min-vh-100 d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="text-center">
                    <!-- Error Code -->
                    <div class="error-code mb-4">
                        <h1 class="display-1 fw-bold text-primary">{{ code|default('404') }}</h1>
                    </div>
                    
                    <!-- Error Message -->
                    <div class="error-message mb-4">
                        <h2 class="h3 mb-3">
                            {% if code == '404' %}
                                Page Not Found
                            {% elseif code == '403' %}
                                Access Forbidden
                            {% elseif code == '500' %}
                                Internal Server Error
                            {% else %}
                                Something went wrong
                            {% endif %}
                        </h2>
                        
                        <p class="text-muted mb-4">
                            {% if message %}
                                {{ message }}
                            {% else %}
                                {% if code == '404' %}
                                    The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
                                {% elseif code == '403' %}
                                    You don't have permission to access this resource.
                                {% elseif code == '500' %}
                                    We're experiencing some technical difficulties. Please try again later.
                                {% else %}
                                    An unexpected error occurred. Please try again or contact support if the problem persists.
                                {% endif %}
                            {% endif %}
                        </p>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="error-actions">
                        <a href="{{ '/'|link }}" class="btn btn-primary me-2">
                            <i class="fas fa-home me-1"></i>
                            Go Home
                        </a>
                        <button onclick="history.back()" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Go Back
                        </button>
                    </div>
                    
                    <!-- Additional Help -->
                    <div class="error-help mt-5">
                        <p class="text-muted mb-2">Need help?</p>
                        <div class="d-flex justify-content-center gap-3">
                            <a href="{{ 'support'|link }}" class="text-decoration-none">
                                <i class="fas fa-life-ring me-1"></i>
                                Contact Support
                            </a>
                            <a href="{{ 'page/faq'|link }}" class="text-decoration-none">
                                <i class="fas fa-question-circle me-1"></i>
                                FAQ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

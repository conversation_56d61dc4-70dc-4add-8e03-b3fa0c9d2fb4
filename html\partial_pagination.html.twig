{% if list.pages > 1 %}
    <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
        <ul class="pagination pagination-modern">
            {% if list.page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ request_uri }}?page=1" aria-label="First">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="{{ request_uri }}?page={{ list.page - 1 }}" aria-label="Previous">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
            {% endif %}

            {% set start_page = max(1, list.page - 2) %}
            {% set end_page = min(list.pages, list.page + 2) %}

            {% if start_page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ request_uri }}?page=1">1</a>
                </li>
                {% if start_page > 2 %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                {% endif %}
            {% endif %}

            {% for page in start_page..end_page %}
                <li class="page-item {% if page == list.page %}active{% endif %}">
                    {% if page == list.page %}
                        <span class="page-link">{{ page }}</span>
                    {% else %}
                        <a class="page-link" href="{{ request_uri }}?page={{ page }}">{{ page }}</a>
                    {% endif %}
                </li>
            {% endfor %}

            {% if end_page < list.pages %}
                {% if end_page < list.pages - 1 %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                {% endif %}
                <li class="page-item">
                    <a class="page-link" href="{{ request_uri }}?page={{ list.pages }}">{{ list.pages }}</a>
                </li>
            {% endif %}

            {% if list.page < list.pages %}
                <li class="page-item">
                    <a class="page-link" href="{{ request_uri }}?page={{ list.page + 1 }}" aria-label="Next">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="{{ request_uri }}?page={{ list.pages }}" aria-label="Last">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>

    <div class="text-center text-muted mt-2">
        <small>
            Showing {{ ((list.page - 1) * list.per_page) + 1 }} to 
            {{ min(list.page * list.per_page, list.total) }} of 
            {{ list.total }} results
        </small>
    </div>
{% endif %}

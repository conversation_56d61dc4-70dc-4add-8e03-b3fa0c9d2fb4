{% extends "base.twig" %}

{% block meta_title %}{{ 'Services'|trans }}{% endblock %}

{% block breadcrumb %}
    <li class="breadcrumb-item active" aria-current="page">{{ 'Services'|trans }}</li>
{% endblock %}

{% block content %}
    <!-- Page Header -->
    <div class="page-header fade-in-up">
        <div class="page-header-content">
            <h1 class="page-title">{{ 'My Services'|trans }}</h1>
            <p class="page-subtitle">{{ 'Manage your active services and subscriptions'|trans }}</p>
        </div>
        <div class="page-header-actions">
            <a href="{{ '/order'|link }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                {{ 'Order New Service'|trans }}
            </a>
        </div>
    </div>

    <!-- Service Statistics -->
    <div class="service-stats fade-in-up">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ service_stats.active|default(0) }}</div>
                        <div class="stat-label">{{ 'Active Services'|trans }}</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-icon suspended">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ service_stats.suspended|default(0) }}</div>
                        <div class="stat-label">{{ 'Suspended'|trans }}</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ service_stats.pending|default(0) }}</div>
                        <div class="stat-label">{{ 'Pending Setup'|trans }}</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ service_stats.expiring_soon|default(0) }}</div>
                        <div class="stat-label">{{ 'Expiring Soon'|trans }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Services Grid -->
    {% if services %}
        <div class="services-grid fade-in-up">
            {% for service in services %}
                <div class="service-card">
                    <div class="service-card-header">
                        <div class="service-info">
                            <h4 class="service-title">{{ service.title }}</h4>
                            <p class="service-description">{{ service.product_title }}</p>
                        </div>
                        <div class="service-status">
                            <span class="status-badge {{ service.status }}">
                                {{ service.status|title }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="service-card-body">
                        <div class="service-details">
                            <div class="service-detail-item">
                                <span class="detail-label">{{ 'Price'|trans }}</span>
                                <span class="detail-value">{{ service.total|money(service.currency) }}/{{ service.period|trans }}</span>
                            </div>
                            
                            {% if service.domain %}
                                <div class="service-detail-item">
                                    <span class="detail-label">{{ 'Domain'|trans }}</span>
                                    <span class="detail-value">{{ service.domain }}</span>
                                </div>
                            {% endif %}
                            
                            <div class="service-detail-item">
                                <span class="detail-label">{{ 'Next Due'|trans }}</span>
                                <span class="detail-value">{{ service.expires_at|date('M j, Y') }}</span>
                            </div>
                            
                            {% if service.created_at %}
                                <div class="service-detail-item">
                                    <span class="detail-label">{{ 'Created'|trans }}</span>
                                    <span class="detail-value">{{ service.created_at|date('M j, Y') }}</span>
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Service Progress (for setup services) -->
                        {% if service.status == 'pending_setup' %}
                            <div class="service-progress">
                                <div class="progress-label">{{ 'Setup Progress'|trans }}</div>
                                <div class="progress">
                                    <div class="progress-bar" style="width: {{ service.setup_progress|default(25) }}%"></div>
                                </div>
                                <div class="progress-text">{{ service.setup_progress|default(25) }}% {{ 'Complete'|trans }}</div>
                            </div>
                        {% endif %}
                        
                        <!-- Service Usage (for hosting services) -->
                        {% if service.usage %}
                            <div class="service-usage">
                                <div class="usage-item">
                                    <span class="usage-label">{{ 'Disk Usage'|trans }}</span>
                                    <div class="usage-bar">
                                        <div class="usage-progress" style="width: {{ (service.usage.disk_used / service.usage.disk_limit * 100)|round }}%"></div>
                                    </div>
                                    <span class="usage-text">{{ service.usage.disk_used }}MB / {{ service.usage.disk_limit }}MB</span>
                                </div>
                                
                                <div class="usage-item">
                                    <span class="usage-label">{{ 'Bandwidth'|trans }}</span>
                                    <div class="usage-bar">
                                        <div class="usage-progress" style="width: {{ (service.usage.bandwidth_used / service.usage.bandwidth_limit * 100)|round }}%"></div>
                                    </div>
                                    <span class="usage-text">{{ service.usage.bandwidth_used }}GB / {{ service.usage.bandwidth_limit }}GB</span>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="service-card-footer">
                        <div class="service-actions">
                            <a href="{{ '/order/service'|link }}/{{ service.id }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-cog me-1"></i>
                                {{ 'Manage'|trans }}
                            </a>
                            
                            {% if service.status == 'active' %}
                                {% if guest.extension_is_on({"mod":"servicehosting"}) %}
                                    <a href="{{ '/servicehosting'|link }}/{{ service.id }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-tools me-1"></i>
                                        {{ 'Control Panel'|trans }}
                                    </a>
                                {% endif %}
                                
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-h"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ '/order/service'|link }}/{{ service.id }}/renew">
                                            <i class="fas fa-redo me-2"></i>{{ 'Renew'|trans }}
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ '/order/service'|link }}/{{ service.id }}/upgrade">
                                            <i class="fas fa-arrow-up me-2"></i>{{ 'Upgrade'|trans }}
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="{{ '/order/service'|link }}/{{ service.id }}/cancel">
                                            <i class="fas fa-times me-2"></i>{{ 'Cancel'|trans }}
                                        </a></li>
                                    </ul>
                                </div>
                            {% endif %}
                            
                            {% if service.status == 'suspended' %}
                                <a href="{{ '/order/service'|link }}/{{ service.id }}/unsuspend" class="btn btn-success btn-sm">
                                    <i class="fas fa-play me-1"></i>
                                    {{ 'Reactivate'|trans }}
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if pagination %}
            <div class="pagination-wrapper fade-in-up">
                {{ pagination|raw }}
            </div>
        {% endif %}
    {% else %}
        <div class="empty-state fade-in-up">
            <i class="fas fa-server fa-3x text-muted mb-4"></i>
            <h4 class="text-muted">{{ 'No Services Found'|trans }}</h4>
            <p class="text-muted">{{ 'You don\'t have any services yet. Start by ordering your first service.'|trans }}</p>
            <a href="{{ '/order'|link }}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>
                {{ 'Order Your First Service'|trans }}
            </a>
        </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="quick-actions-section fade-in-up">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">{{ 'Quick Actions'|trans }}</h4>
            </div>
            <div class="card-body">
                <div class="quick-actions-grid">
                    <a href="{{ '/order'|link }}" class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="quick-action-content">
                            <h5>{{ 'Order New Service'|trans }}</h5>
                            <p>{{ 'Browse our hosting plans and order a new service'|trans }}</p>
                        </div>
                    </a>
                    
                    <a href="{{ '/invoice'|link }}" class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="quick-action-content">
                            <h5>{{ 'View Invoices'|trans }}</h5>
                            <p>{{ 'Check your billing history and pay outstanding invoices'|trans }}</p>
                        </div>
                    </a>
                    
                    {% if guest.extension_is_on({"mod":"support"}) %}
                        <a href="{{ '/support'|link }}" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-life-ring"></i>
                            </div>
                            <div class="quick-action-content">
                                <h5>{{ 'Get Support'|trans }}</h5>
                                <p>{{ 'Need help? Contact our support team'|trans }}</p>
                            </div>
                        </a>
                    {% endif %}
                    
                    <a href="{{ '/client/profile'|link }}" class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="fas fa-user-cog"></i>
                        </div>
                        <div class="quick-action-content">
                            <h5>{{ 'Account Settings'|trans }}</h5>
                            <p>{{ 'Update your profile and account preferences'|trans }}</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block css %}
<style>
    /* Service Card Specific Styles */
    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: var(--widdx-spacing-6);
        margin-bottom: var(--widdx-spacing-8);
    }
    
    .service-card {
        background: var(--widdx-white);
        border: 1px solid var(--widdx-gray-200);
        border-radius: var(--widdx-radius-lg);
        overflow: hidden;
        transition: all var(--widdx-transition-normal);
        display: flex;
        flex-direction: column;
    }
    
    .service-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--widdx-shadow-xl);
        border-color: var(--widdx-primary);
    }
    
    .service-card-header {
        padding: var(--widdx-spacing-6);
        border-bottom: 1px solid var(--widdx-gray-100);
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }
    
    .service-title {
        font-size: var(--widdx-font-size-lg);
        font-weight: 600;
        color: var(--widdx-gray-900);
        margin-bottom: var(--widdx-spacing-1);
    }
    
    .service-description {
        color: var(--widdx-gray-600);
        font-size: var(--widdx-font-size-sm);
        margin: 0;
    }
    
    .service-card-body {
        padding: var(--widdx-spacing-6);
        flex: 1;
    }
    
    .service-details {
        margin-bottom: var(--widdx-spacing-6);
    }
    
    .service-detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--widdx-spacing-2) 0;
        border-bottom: 1px solid var(--widdx-gray-100);
    }
    
    .service-detail-item:last-child {
        border-bottom: none;
    }
    
    .detail-label {
        font-size: var(--widdx-font-size-sm);
        color: var(--widdx-gray-500);
        font-weight: 500;
    }
    
    .detail-value {
        font-size: var(--widdx-font-size-sm);
        color: var(--widdx-gray-900);
        font-weight: 600;
    }
    
    /* Service Progress */
    .service-progress {
        margin-top: var(--widdx-spacing-4);
        padding: var(--widdx-spacing-4);
        background: var(--widdx-gray-50);
        border-radius: var(--widdx-radius-md);
    }
    
    .progress-label {
        font-size: var(--widdx-font-size-sm);
        font-weight: 500;
        color: var(--widdx-gray-700);
        margin-bottom: var(--widdx-spacing-2);
    }
    
    .progress {
        height: 8px;
        background: var(--widdx-gray-200);
        border-radius: var(--widdx-radius-full);
        overflow: hidden;
        margin-bottom: var(--widdx-spacing-2);
    }
    
    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, var(--widdx-primary), var(--widdx-accent));
        transition: width var(--widdx-transition-normal);
    }
    
    .progress-text {
        font-size: var(--widdx-font-size-xs);
        color: var(--widdx-gray-600);
        text-align: center;
    }
    
    /* Service Usage */
    .service-usage {
        margin-top: var(--widdx-spacing-4);
        padding: var(--widdx-spacing-4);
        background: var(--widdx-gray-50);
        border-radius: var(--widdx-radius-md);
    }
    
    .usage-item {
        margin-bottom: var(--widdx-spacing-3);
    }
    
    .usage-item:last-child {
        margin-bottom: 0;
    }
    
    .usage-label {
        font-size: var(--widdx-font-size-sm);
        font-weight: 500;
        color: var(--widdx-gray-700);
        display: block;
        margin-bottom: var(--widdx-spacing-1);
    }
    
    .usage-bar {
        height: 6px;
        background: var(--widdx-gray-200);
        border-radius: var(--widdx-radius-full);
        overflow: hidden;
        margin-bottom: var(--widdx-spacing-1);
    }
    
    .usage-progress {
        height: 100%;
        background: linear-gradient(90deg, var(--widdx-success), #059669);
        transition: width var(--widdx-transition-normal);
    }
    
    .usage-text {
        font-size: var(--widdx-font-size-xs);
        color: var(--widdx-gray-600);
    }
    
    .service-card-footer {
        padding: var(--widdx-spacing-4) var(--widdx-spacing-6);
        background: var(--widdx-gray-50);
        border-top: 1px solid var(--widdx-gray-100);
    }
    
    .service-actions {
        display: flex;
        gap: var(--widdx-spacing-2);
        align-items: center;
        flex-wrap: wrap;
    }
    
    /* Quick Actions */
    .quick-actions-section {
        margin-top: var(--widdx-spacing-8);
    }
    
    .quick-actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--widdx-spacing-4);
    }
    
    .quick-action-card {
        display: flex;
        align-items: center;
        gap: var(--widdx-spacing-4);
        padding: var(--widdx-spacing-4);
        border: 1px solid var(--widdx-gray-200);
        border-radius: var(--widdx-radius-md);
        text-decoration: none;
        color: inherit;
        transition: all var(--widdx-transition-fast);
    }
    
    .quick-action-card:hover {
        border-color: var(--widdx-primary);
        transform: translateY(-2px);
        box-shadow: var(--widdx-shadow-md);
        text-decoration: none;
        color: inherit;
    }
    
    .quick-action-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, var(--widdx-primary), var(--widdx-accent));
        border-radius: var(--widdx-radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--widdx-white);
        font-size: var(--widdx-font-size-lg);
        flex-shrink: 0;
    }
    
    .quick-action-content h5 {
        font-size: var(--widdx-font-size-base);
        font-weight: 600;
        color: var(--widdx-gray-900);
        margin-bottom: var(--widdx-spacing-1);
    }
    
    .quick-action-content p {
        font-size: var(--widdx-font-size-sm);
        color: var(--widdx-gray-600);
        margin: 0;
    }
    
    /* Status Icons */
    .stat-icon.suspended {
        background: linear-gradient(135deg, var(--widdx-warning), #d97706);
    }
    
    .stat-icon.pending {
        background: linear-gradient(135deg, var(--widdx-info), #0284c7);
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .services-grid {
            grid-template-columns: 1fr;
        }
        
        .service-actions {
            flex-direction: column;
        }
        
        .service-actions .btn,
        .service-actions .btn-group {
            width: 100%;
        }
        
        .quick-actions-grid {
            grid-template-columns: 1fr;
        }
        
        .quick-action-card {
            flex-direction: column;
            text-align: center;
        }
    }
</style>
{% endblock %}

/**
 * WIDDX Theme - Pure JavaScript (No Dependencies)
 * Modern, lightweight theme functionality
 */

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
});

/**
 * Initialize all theme functionality
 */
function initializeTheme() {
    initializeDropdowns();
    initializeForms();
    initializeAnimations();
    initializeNavigation();
    initializeMessages();
    initializeFOSSBilling();
}

/**
 * Initialize dropdown functionality
 */
function initializeDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown');

    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');

        if (toggle && menu) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Close other dropdowns
                dropdowns.forEach(otherDropdown => {
                    if (otherDropdown !== dropdown) {
                        const otherMenu = otherDropdown.querySelector('.dropdown-menu');
                        const otherToggle = otherDropdown.querySelector('.dropdown-toggle');
                        if (otherMenu) {
                            otherMenu.classList.remove('show');
                        }
                        if (otherToggle) {
                            otherToggle.setAttribute('aria-expanded', 'false');
                        }
                    }
                });

                // Toggle current dropdown
                menu.classList.toggle('show');
                const expanded = menu.classList.contains('show');
                this.setAttribute('aria-expanded', expanded);
            });
        }
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function() {
        dropdowns.forEach(dropdown => {
            const menu = dropdown.querySelector('.dropdown-menu');
            const toggle = dropdown.querySelector('.dropdown-toggle');
            if (menu) {
                menu.classList.remove('show');
            }
            if (toggle) {
                toggle.setAttribute('aria-expanded', 'false');
            }
        });
    });

    // Handle keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            dropdowns.forEach(dropdown => {
                const menu = dropdown.querySelector('.dropdown-menu');
                const toggle = dropdown.querySelector('.dropdown-toggle');
                if (menu) {
                    menu.classList.remove('show');
                }
                if (toggle) {
                    toggle.setAttribute('aria-expanded', 'false');
                    toggle.focus();
                }
            });
        }
    });
}

/**
 * Initialize form enhancements
 */
function initializeForms() {
    // Add focus/blur effects to form controls
    const formControls = document.querySelectorAll('.form-control, .form-select');
    
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        control.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
}

/**
 * Initialize animations and scroll effects
 */
function initializeAnimations() {
    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = ${index * 0.1}s;
        card.classList.add('fade-in');
    });
}

/**
 * Initialize navigation functionality
 */
function initializeNavigation() {
    // Navbar toggle functionality
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (navbarToggler && navbarCollapse) {
        navbarToggler.addEventListener('click', function() {
            navbarCollapse.classList.toggle('show');
            const expanded = navbarCollapse.classList.contains('show');
            navbarToggler.setAttribute('aria-expanded', expanded);
        });
    }

    // Close navbar when clicking outside
    document.addEventListener('click', function(e) {
        if (navbarCollapse && navbarToggler &&
            !navbarCollapse.contains(e.target) &&
            !navbarToggler.contains(e.target)) {
            navbarCollapse.classList.remove('show');
            navbarToggler.setAttribute('aria-expanded', 'false');
        }
    });

    // Language selector functionality
    const languageSelector = document.querySelector('.js-language-selector');
    if (languageSelector) {
        languageSelector.addEventListener('change', function() {
            const selectedLocale = this.value;
            // Update URL with language parameter
            const url = new URL(window.location);
            url.searchParams.set('locale', selectedLocale);
            window.location.href = url.toString();
        });
    }

    // Add active class to current page navigation
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
}

/**
 * Initialize message system
 */
function initializeMessages() {
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
}

/**
 * FOSSBilling-specific functionality
 */
function initializeFOSSBilling() {
    // Enable Bootstrap Tooltips
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    [...tooltipTriggerList].map(tooltipTriggerEl => {
        // Simple tooltip implementation without Bootstrap dependency
        tooltipTriggerEl.addEventListener('mouseenter', function() {
            const title = this.getAttribute('title') || this.getAttribute('data-bs-title');
            if (title) {
                const tooltip = document.createElement('div');
                tooltip.className = 'widdx-tooltip';
                tooltip.textContent = title;
                document.body.appendChild(tooltip);

                const rect = this.getBoundingClientRect();
                tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';

                this._tooltip = tooltip;
            }
        });

        tooltipTriggerEl.addEventListener('mouseleave', function() {
            if (this._tooltip) {
                this._tooltip.remove();
                this._tooltip = null;
            }
        });
    });

    // Flash message management
    window.flashMessage = ({message = '', reload = false, type = 'info'}) => {
        let key = 'flash-message';
        let sessionMessage = sessionStorage.getItem(key);
        if (message === '' && sessionMessage) {
            showMessage(sessionMessage, type);
            sessionStorage.removeItem(key);
            return;
        }
        if (message) {
            sessionStorage.setItem(key, message);
            if (typeof reload === 'boolean' && reload) {
                window.location.reload();
            } else if (typeof reload === 'string') {
                window.location.href = reload;
            }
        }
    };

    // Initialize flash messages
    flashMessage({});

    // Add asterisk to required field labels
    const requiredInputs = document.querySelectorAll('input[required], textarea[required]');
    requiredInputs.forEach(input => {
        const label = input.previousElementSibling;
        const isAuth = input.parentElement.parentElement.classList.contains('auth');
        if (!isAuth && label && label.tagName.toLowerCase() === 'label') {
            const asterisk = document.createElement('span');
            asterisk.textContent = ' *';
            asterisk.classList.add('text-danger');
            label.appendChild(asterisk);
        }
    });

    // Currency selector functionality
    const currencySelector = document.querySelectorAll('select.currency_selector');
    currencySelector.forEach(function (select) {
        select.addEventListener('change', function () {
            // Simple API call without jQuery dependency
            fetch('/api/guest/cart/set_currency', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({currency: select.value})
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showMessage(data.error.message, 'error');
                } else {
                    location.reload();
                }
            })
            .catch(error => {
                showMessage('An error occurred', 'error');
            });
        });
    });

    // Initialize API forms and links
    initializeAPIForms();
    initializeAPILinks();
}

/**
 * Initialize API forms functionality
 */
function initializeAPIForms() {
    const apiForms = document.querySelectorAll('form.api-form');
    apiForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(form);
            const action = form.getAttribute('action') || form.getAttribute('data-api-url');

            if (!action) return;

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
            const originalText = submitBtn ? submitBtn.textContent : '';
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.textContent = 'Loading...';
            }

            fetch(action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showMessage(data.error.message || 'An error occurred', 'error');
                } else {
                    if (data.result && data.result.message) {
                        showMessage(data.result.message, 'success');
                    }
                    if (form.getAttribute('data-api-reload') === 'true') {
                        setTimeout(() => location.reload(), 1000);
                    }
                }
            })
            .catch(error => {
                showMessage('An error occurred', 'error');
            })
            .finally(() => {
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.textContent = originalText;
                }
            });
        });
    });
}

/**
 * Initialize API links functionality
 */
function initializeAPILinks() {
    const apiLinks = document.querySelectorAll('a.api-link');
    apiLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const url = link.getAttribute('href') || link.getAttribute('data-api-url');
            const confirm = link.getAttribute('data-api-confirm');

            if (!url) return;

            if (confirm && !window.confirm(confirm)) {
                return;
            }

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showMessage(data.error.message || 'An error occurred', 'error');
                } else {
                    if (data.result && data.result.message) {
                        showMessage(data.result.message, 'success');
                    }
                    if (link.getAttribute('data-api-reload') === 'true') {
                        setTimeout(() => location.reload(), 1000);
                    }
                    if (link.getAttribute('data-api-redirect')) {
                        setTimeout(() => {
                            window.location.href = link.getAttribute('data-api-redirect');
                        }, 1000);
                    }
                }
            })
            .catch(error => {
                showMessage('An error occurred', 'error');
            });
        });
    });
}

/**
 * Show message function
 */
function showMessage(message, type = 'info') {
    const messageContainer = document.createElement('div');
    messageContainer.className = `alert alert-${type} alert-dismissible fade show`;
    messageContainer.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Insert at the top of the main content
    const mainContent = document.querySelector('.main-content') || document.querySelector('main') || document.body;
    mainContent.insertBefore(messageContainer, mainContent.firstChild);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        messageContainer.remove();
    }, 5000);
}

// Export functions for global use
window.WIDDXTheme = {
    initializeTheme,
    showMessage,
    flashMessage: window.flashMessage
};

// Make FOSSBilling object available globally for compatibility
window.FOSSBilling = window.FOSSBilling || {};
window.FOSSBilling.message = showMessage;
